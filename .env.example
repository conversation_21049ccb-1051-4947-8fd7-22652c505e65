# ===========================================
# EXAMPLE ENVIRONMENT CONFIGURATION
# ===========================================

# Development Server
VITE_PORT=3000
VITE_HOST=localhost
VITE_USE_HTTPS=false

# Application Window
VITE_WINDOW_WIDTH=1536
VITE_WINDOW_HEIGHT=864

# Application Info
VITE_APP_NAME=VuetifyElectronStarter
VITE_APP_VERSION=0.0.1
VITE_APP_AUTHOR=author

# Theme
VITE_DEFAULT_THEME=dark

# Development
VITE_OPEN_DEVTOOLS=true

# Debug (conditional logic)
DEBUG_BUILD=true

# Playwright with Fingerprints
# Note: On first launch, the engine will be downloaded to this folder (800+ MB, depends on internet speed)
PLAYWRIGHT_FINGERPRINTS_WORKING_FOLDER=.data_playwright_with_fingerprints
