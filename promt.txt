 Compare the current branch with the `develop` branch and generate a commit message based on the
 differences ('git diff origin/develop`).
As a result, create the file `commit_message.txt`.
Make sure you're using The Conventional Commits specification and add a description to the message.

###################################

To finally change, run linters and tests to check if the changes are okay.
Compare the current branch with the `develop` branch (`git diff origin/develop`) to see the differences.
Review all changes, check that we have only needed files, not any artifacts from iterations, etc. Also, update docs.

####################################
Review ONLY TESTS:

To finally change, run linters on tests to check if the changes are okay.
Compare the current branch with the `develop` branch, Review all changes, check that we have only the necessary files,
not any artifacts from iterations, etc. Also, update docs if needed.
