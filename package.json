{"name": "ghost-automator-pro", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest run", "test:unit:watch": "vitest", "test:unit:coverage": "vitest run --coverage", "test:e2e:prepare": "cypress install", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:format": "npm run lint:fix && npm run format", "format": "prettier --write src/ cypress/ *.ts"}, "dependencies": {"flowbite": "3.1.2", "flowbite-vue": "0.2.1", "pinia": "3.0.3", "vue": "3.5.16", "vue-router": "4.5.1"}, "devDependencies": {"@tailwindcss/vite": "4.1.8", "@tsconfig/node22": "22.0.2", "@types/jsdom": "21.1.7", "@types/node": "22.15.30", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.2.0", "@vitest/eslint-plugin": "1.2.1", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.5.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "cypress": "14.4.1", "eslint": "9.28.0", "eslint-plugin-cypress": "5.1.0", "eslint-plugin-vue": "10.2.0", "jiti": "2.4.2", "jsdom": "26.1.0", "npm-run-all2": "8.0.4", "prettier": "3.5.3", "start-server-and-test": "2.0.12", "tailwindcss": "4.1.8", "typescript": "~5.8.0", "vite": "6.3.5", "vite-plugin-vue-devtools": "7.7.6", "vitest": "3.2.2", "vue-tsc": "2.2.10"}}