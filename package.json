{"name": "vuetify-electron-starter", "description": "A modern Electron application built with Vue 3, Vuetify, and TypeScript", "author": "serger<PERSON>", "private": true, "type": "module", "version": "0.0.2", "main": "./build-electron/electron-main/index.js", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"{src,tests}/**/*.{js,ts,vue,css,scss,json}\" \"*.ts\"", "format:check": "prettier --check \"{src,tests}/**/*.{js,ts,vue,css,scss,json}\" \"*.ts\"", "format:all": "prettier --write \"**/*.{js,ts,vue,css,scss,json,md}\" \"*.ts\"", "lint:format": "npm run lint:fix && npm run format", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "start-server-and-test dev http://localhost:3000 \"cypress run\"", "test:e2e:open": "start-server-and-test dev http://localhost:3000 \"cypress open\"", "test:all": "npm run test && npm run test:e2e", "ci": "npm run lint && npm run type-check && npm run test:all", "electron:dev": "electron-vite dev", "electron:build": "electron-vite build && electron-builder", "electron:preview": "electron-vite preview", "electron:pack": "electron-vite build && electron-builder --dir", "commit": "cz", "version:dry": "standard-version --dry-run", "version:auto": "standard-version", "version:patch": "standard-version --release-as patch", "version:minor": "standard-version --release-as minor", "version:major": "standard-version --release-as major"}, "dependencies": {"@fontsource/roboto": "5.2.5", "@mdi/font": "7.4.47", "env-var": "7.5.0", "playwright": "1.52.0", "playwright-with-fingerprints": "2.2.0", "vue": "3.5.16", "vuetify": "3.8.8"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.29", "@vitejs/plugin-vue": "5.2.4", "@vitest/coverage-v8": "3.2.1", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.5.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "commitizen": "^4.3.1", "cypress": "14.4.1", "cz-conventional-changelog": "^3.3.0", "electron": "36.4.0", "electron-builder": "26.0.12", "electron-vite": "3.1.0", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-config-vuetify": "4.0.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-vue": "10.1.0", "happy-dom": "17.6.3", "memfs": "4.17.2", "npm-run-all2": "8.0.4", "pinia": "3.0.3", "prettier": "3.5.3", "sass": "1.89.1", "standard-version": "^9.5.0", "start-server-and-test": "2.0.12", "typescript": "~5.8.3", "unplugin-auto-import": "19.3.0", "unplugin-fonts": "1.3.1", "unplugin-vue-components": "28.7.0", "unplugin-vue-router": "0.12.0", "vite": "6.3.5", "vite-plugin-vue-layouts-next": "0.1.3", "vite-plugin-vuetify": "2.1.1", "vitest": "3.2.1", "vue-router": "4.5.1", "vue-tsc": "2.2.10"}, "build": {"appId": "com.vuetify.electron.starter", "productName": "VuetifyElectronStarter", "copyright": "Copyright © 2025 ${author}", "electronVersion": "36.3.2", "directories": {"output": "dist-electron/${version}", "buildResources": "build-resources"}, "files": ["build-electron/**/*", "node_modules/**/*", "package.json", "!node_modules/**/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/**/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/**/*.d.ts", "!node_modules/**/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!**/node_modules/fsevents/**/*", "!**/node_modules/@parcel/watcher-darwin*/**/*", "!**/node_modules/@parcel/watcher-linux*/**/*", "!**/node_modules/@esbuild/darwin*/**/*", "!**/node_modules/@esbuild/linux*/**/*"], "extraMetadata": {"main": "./build-electron/electron-main/index.js"}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "asarUnpack": ["**/node_modules/playwright/**/*", "**/node_modules/playwright-with-fingerprints/**/*"], "win": {"target": [{"target": "dir", "arch": ["x64"]}], "icon": "build-resources/v-logo.ico", "artifactName": "${productName}-${version}-${arch}", "requestedExecutionLevel": "asInvoker", "verifyUpdateCodeSignature": false}, "publish": [{"provider": "github", "owner": "serger<PERSON>", "repo": "vuetify-electron-starter", "releaseType": "release"}]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}