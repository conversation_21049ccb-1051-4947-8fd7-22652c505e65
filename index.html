<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ghost Automator Pro</title>
    <script>
      // On page load or when changing themes, best to add inline in `head` to avoid FOUC
      // Default to dark mode
      if (localStorage.getItem('color-theme') === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        document.documentElement.classList.add('dark');
        if (!localStorage.getItem('color-theme')) {
          localStorage.setItem('color-theme', 'dark');
        }
      }
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
