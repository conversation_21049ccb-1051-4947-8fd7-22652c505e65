docs(project): add comprehensive documentation and development guidelines

This commit adds extensive documentation to the project:
- Add DEVELOPMENT.md with technical information and guidelines for developers
- Completely revamp README.md with detailed project description and features
- Move original Vue template README to README_vue.md
- Add implementation guide with functional requirements and roadmap
- Add UI design requirements with detailed specifications
- Update .gitignore to exclude .research/ directory

These documentation improvements provide clear guidelines for development,
implementation, and UI design, establishing a solid foundation for the project.
