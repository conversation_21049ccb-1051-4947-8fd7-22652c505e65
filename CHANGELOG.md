# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## 0.0.2 (2025-06-05)


### Bug Fixes

* **build:** fsevents ([ae521b8](https://github.com/sergerdn/vuetify-electron-starter/commit/ae521b8860dc0bd6a2ce2aabffc1175764977eb1))


### Features

* add Electron support ([#4](https://github.com/sergerdn/vuetify-electron-starter/issues/4)) ([c857cb6](https://github.com/sergerdn/vuetify-electron-starter/commit/c857cb608e869e7910eb01f0ff86c7d5bd83dd9c))
* add playwright with fingerprints ([#10](https://github.com/sergerdn/vuetify-electron-starter/issues/10)) ([dd19c63](https://github.com/sergerdn/vuetify-electron-starter/commit/dd19c63953ff4ab6db670f02f7f218cf7b0c844f))
* **app:** initial ([8798a78](https://github.com/sergerdn/vuetify-electron-starter/commit/8798a7847bdb59474f30761a06a9c6fb825fc1a8))
* **config:** implement a type-safe configuration system ([#7](https://github.com/sergerdn/vuetify-electron-starter/issues/7)) ([356f2ea](https://github.com/sergerdn/vuetify-electron-starter/commit/356f2ea4fe47e98b6b3c750de4a84efda17fa45d))
* improve electron support ([#5](https://github.com/sergerdn/vuetify-electron-starter/issues/5)) ([e18e7f1](https://github.com/sergerdn/vuetify-electron-starter/commit/e18e7f1505dd047493c844f703cb2e37017d310e))
* **playwright:** add browser automation ([#9](https://github.com/sergerdn/vuetify-electron-starter/issues/9)) ([f2d0057](https://github.com/sergerdn/vuetify-electron-starter/commit/f2d0057b756f009cd40b5e6e4688fe09fecbc521))
* **tooling:** add dependency ([#1](https://github.com/sergerdn/vuetify-electron-starter/issues/1)) ([7b35a5e](https://github.com/sergerdn/vuetify-electron-starter/commit/7b35a5e2e00e36266b640dcbabfb3c77719ecb72))
