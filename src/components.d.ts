/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./components/AppFooter.vue')['default']
    Dashboard: typeof import('./components/Dashboard.vue')['default']
    ElectronIntegration: typeof import('./components/ElectronIntegration.vue')['default']
    FingerprintPlaywrightIntegration: typeof import('./components/FingerprintPlaywrightIntegration.vue')['default']
    PlaywrightIntegration: typeof import('./components/PlaywrightIntegration.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
