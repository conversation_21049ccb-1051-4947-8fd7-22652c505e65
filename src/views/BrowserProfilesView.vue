<script setup lang="ts">
import {
  <PERSON>wb<PERSON><PERSON><PERSON>,
  Fwb<PERSON><PERSON>ge,
  Fwb<PERSON><PERSON>,
  FwbTableBody,
  FwbTableCell,
  FwbTableHead,
  FwbTableHeadCell,
  FwbTableRow,
} from 'flowbite-vue'
</script>

<template>
  <!-- <PERSON> Header -->
  <div
    class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="w-full mb-1">
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a
                href="#"
                class="inline-flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white"
              >
                <svg
                  class="w-5 h-5 mr-2.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                  ></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page"
                  >Browser Profiles</span
                >
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Browser Profiles (FM3 Compatible)
        </h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          View and launch browser profiles created in FingerPrint Manager 3.0 with superior user experience.
        </p>
        <div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
          <p class="text-sm text-blue-800 dark:text-blue-200">
            <strong>MVP:</strong> Reads existing FM3 profiles from <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">C:\Users\<USER>\AppData\Local\BASProfileManager\</code> and launches them without embedded QT windows.
          </p>
        </div>
      </div>
      <div class="sm:flex">
        <div
          class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700"
        >
          <form class="lg:pr-3" action="#" method="GET">
            <label for="profiles-search" class="sr-only">Search</label>
            <div class="relative mt-1 lg:w-64 xl:w-96">
              <input
                type="text"
                name="email"
                id="profiles-search"
                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search for profiles"
              />
            </div>
          </form>
        </div>
        <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
          <!-- MVP: Profile creation and import features will be added in future phases -->
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-4">
    <!-- Total Profiles -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >3</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Ready to use
          </span>
        </p>
      </div>
    </div>

    <!-- Active Sessions -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Sessions</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >1</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-blue-500 dark:text-blue-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Currently running
          </span>
        </p>
      </div>
    </div>

    <!-- FM3 Profiles -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">FM3 Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >3</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Imported successfully
          </span>
        </p>
      </div>
    </div>

    <!-- System Status -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">System Status</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >Online</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            All systems operational
          </span>
        </p>
      </div>
    </div>
  </div>

  <!-- Browser Profiles Table -->
  <div class="flex flex-col mt-6">
    <div class="overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden shadow">
          <FwbTable>
            <FwbTableHead class="bg-gray-50 dark:bg-gray-700">
              <FwbTableHeadCell class="w-4">
                <label for="checkbox-all" class="sr-only">checkbox</label>
                <input
                  id="checkbox-all"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">NAME</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">TARGET WEBSITE</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">BROWSER TYPE</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">VERSION</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">PROXY</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">CREATED</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">LAST USED</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">STATUS</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">TAGS</FwbTableHeadCell>
              <FwbTableHeadCell class="text-right">ACTIONS</FwbTableHeadCell>
            </FwbTableHead>
            <FwbTableBody class="bg-white dark:bg-gray-800">
              <!-- Profile 1 -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-1"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <div
                      class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 dark:bg-blue-900"
                    >
                      <svg
                        class="w-4 h-4 text-blue-600 dark:text-blue-300"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div class="text-base font-semibold">Profile_Chrome_001</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        Chrome automation profile
                      </div>
                    </div>
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16c-.169 1.858-.896 3.616-2.001 4.72C14.27 14.176 13.14 14.6 12 14.6s-2.27-.424-3.567-1.72c-1.105-1.104-1.832-2.862-2.001-4.72-.016-.176.054-.35.19-.472.136-.122.32-.17.496-.129 1.633.382 3.333.382 4.966 0 .176-.041.36.007.496.129.136.122.206.296.19.472z"
                      />
                    </svg>
                    Chrome
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="green" size="sm">Active</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="default" size="sm">FM3</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  2 hours ago
                </FwbTableCell>
                <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                  <FwbButton size="sm" color="blue">Launch</FwbButton>
                  <!-- MVP: Edit functionality will be added in future phases -->
                </FwbTableCell>
              </FwbTableRow>

              <!-- Profile 2 -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-2"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <div
                      class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3 dark:bg-orange-900"
                    >
                      <svg
                        class="w-4 h-4 text-orange-600 dark:text-orange-300"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div class="text-base font-semibold">Profile_Edge_Dev</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        Edge development profile
                      </div>
                    </div>
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16c-.169 1.858-.896 3.616-2.001 4.72C14.27 14.176 13.14 14.6 12 14.6s-2.27-.424-3.567-1.72c-1.105-1.104-1.832-2.862-2.001-4.72-.016-.176.054-.35.19-.472.136-.122.32-.17.496-.129 1.633.382 3.333.382 4.966 0 .176-.041.36.007.496.129.136.122.206.296.19.472z"
                      />
                    </svg>
                    Edge
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="default" size="sm">Idle</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="default" size="sm">FM3</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  1 day ago
                </FwbTableCell>
                <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                  <FwbButton size="sm" color="blue">Launch</FwbButton>
                  <!-- MVP: Edit functionality will be added in future phases -->
                </FwbTableCell>
              </FwbTableRow>

              <!-- Profile 3 -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-3"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <div
                      class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 dark:bg-green-900"
                    >
                      <svg
                        class="w-4 h-4 text-green-600 dark:text-green-300"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div class="text-base font-semibold">Profile_Opera_Social</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        Opera social media profile
                      </div>
                    </div>
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                      <path
                        d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16c-.169 1.858-.896 3.616-2.001 4.72C14.27 14.176 13.14 14.6 12 14.6s-2.27-.424-3.567-1.72c-1.105-1.104-1.832-2.862-2.001-4.72-.016-.176.054-.35.19-.472.136-.122.32-.17.496-.129 1.633.382 3.333.382 4.966 0 .176-.041.36.007.496.129.136.122.206.296.19.472z"
                      />
                    </svg>
                    Opera
                  </div>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="default" size="sm">Idle</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  <FwbBadge type="default" size="sm">FM3</FwbBadge>
                </FwbTableCell>
                <FwbTableCell
                  class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                >
                  3 days ago
                </FwbTableCell>
                <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                  <FwbButton size="sm" color="blue">Launch</FwbButton>
                  <!-- MVP: Edit functionality will be added in future phases -->
                </FwbTableCell>
              </FwbTableRow>
            </FwbTableBody>
          </FwbTable>
        </div>
      </div>
    </div>
  </div>
</template>
