<script setup lang="ts">
import {
  FwbCard,
  FwbButton,
  FwbTimeline,
  FwbTimelineItem,
  FwbTimelinePoint,
  FwbTimelineContent,
  FwbTimelineTitle,
  FwbTimelineBody,
  FwbTimelineTime,
} from 'flowbite-vue'
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <!-- About Header -->
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        About Ghost Automator Pro
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
        A powerful automation platform designed to streamline your Ghost CMS workflow and enhance
        your content management experience.
      </p>
    </div>

    <!-- Mission Card -->
    <div class="mb-12">
      <FwbCard>
        <template #header>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Our Mission</h2>
        </template>
        <p class="text-lg text-gray-700 dark:text-gray-400 leading-relaxed">
          We believe that content creators should focus on what they do best - creating amazing
          content. Ghost Automator Pro eliminates the repetitive tasks and technical complexities,
          allowing you to concentrate on storytelling and audience engagement.
        </p>
        <div class="mt-6">
          <FwbButton color="blue"> Learn More About Our Vision </FwbButton>
        </div>
      </FwbCard>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
      <FwbCard>
        <template #header>
          <h3 class="text-2xl font-bold text-gray-900 dark:text-white">🚀 Automation First</h3>
        </template>
        <p class="text-gray-700 dark:text-gray-400">
          Built from the ground up with automation in mind. Schedule posts, manage content
          workflows, and optimize your publishing process.
        </p>
      </FwbCard>

      <FwbCard>
        <template #header>
          <h3 class="text-2xl font-bold text-gray-900 dark:text-white">📊 Data-Driven Insights</h3>
        </template>
        <p class="text-gray-700 dark:text-gray-400">
          Make informed decisions with comprehensive analytics and performance metrics that help you
          understand your audience better.
        </p>
      </FwbCard>

      <FwbCard>
        <template #header>
          <h3 class="text-2xl font-bold text-gray-900 dark:text-white">🔧 Developer Friendly</h3>
        </template>
        <p class="text-gray-700 dark:text-gray-400">
          Built with modern technologies including Vue 3, TypeScript, and Flowbite. Extensible and
          customizable to fit your specific needs.
        </p>
      </FwbCard>

      <FwbCard>
        <template #header>
          <h3 class="text-2xl font-bold text-gray-900 dark:text-white">🌟 User Experience</h3>
        </template>
        <p class="text-gray-700 dark:text-gray-400">
          Intuitive interface designed for both beginners and power users. Clean, responsive design
          that works seamlessly across all devices.
        </p>
      </FwbCard>
    </div>

    <!-- Development Timeline -->
    <div class="mb-12">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-8">
        Development Timeline
      </h2>
      <FwbTimeline>
        <FwbTimelineItem>
          <FwbTimelinePoint />
          <FwbTimelineContent>
            <FwbTimelineTime>January 2024</FwbTimelineTime>
            <FwbTimelineTitle>Project Inception</FwbTimelineTitle>
            <FwbTimelineBody>
              Initial concept and planning phase. Research into Ghost CMS automation needs and user
              requirements gathering.
            </FwbTimelineBody>
          </FwbTimelineContent>
        </FwbTimelineItem>

        <FwbTimelineItem>
          <FwbTimelinePoint />
          <FwbTimelineContent>
            <FwbTimelineTime>March 2024</FwbTimelineTime>
            <FwbTimelineTitle>Core Development</FwbTimelineTitle>
            <FwbTimelineBody>
              Development of core automation features and integration with Ghost CMS API.
              Implementation of user authentication and basic workflow management.
            </FwbTimelineBody>
          </FwbTimelineContent>
        </FwbTimelineItem>

        <FwbTimelineItem>
          <FwbTimelinePoint />
          <FwbTimelineContent>
            <FwbTimelineTime>June 2024</FwbTimelineTime>
            <FwbTimelineTitle>UI/UX Enhancement</FwbTimelineTitle>
            <FwbTimelineBody>
              Integration of Flowbite Vue components for improved user experience. Responsive design
              implementation and accessibility improvements.
            </FwbTimelineBody>
          </FwbTimelineContent>
        </FwbTimelineItem>

        <FwbTimelineItem>
          <FwbTimelinePoint />
          <FwbTimelineContent>
            <FwbTimelineTime>Present</FwbTimelineTime>
            <FwbTimelineTitle>Continuous Improvement</FwbTimelineTitle>
            <FwbTimelineBody>
              Ongoing development with new features, performance optimizations, and user feedback
              integration.
            </FwbTimelineBody>
          </FwbTimelineContent>
        </FwbTimelineItem>
      </FwbTimeline>
    </div>

    <!-- Contact Section -->
    <div class="text-center">
      <FwbCard>
        <template #header>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Get In Touch</h2>
        </template>
        <p class="text-lg text-gray-700 dark:text-gray-400 mb-6">
          Have questions or suggestions? We'd love to hear from you!
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <FwbButton color="blue"> Contact Support </FwbButton>
          <FwbButton color="alternative"> Join Community </FwbButton>
        </div>
      </FwbCard>
    </div>
  </div>
</template>
