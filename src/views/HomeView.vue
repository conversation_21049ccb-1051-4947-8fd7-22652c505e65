<script setup lang="ts">
// import { FwbButton } from 'flowbite-vue' // Removed for MVP
</script>

<template>
  <!-- <PERSON> Header -->
  <div
    class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="w-full mb-1">
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a
                href="#"
                class="inline-flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white"
              >
                <svg
                  class="w-5 h-5 mr-2.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                  ></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page"
                  >Dashboard</span
                >
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Ghost Automator Pro Dashboard
        </h1>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-4">
    <!-- Total Profiles Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Browser Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >0</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-gray-500 dark:text-gray-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Ready to start
          </span>
        </p>
      </div>
      <div class="w-full max-w-none">
        <div id="main-chart"></div>
      </div>
    </div>

    <!-- Active Sessions Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Sessions</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >0</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-gray-500 dark:text-gray-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            No active sessions
          </span>
        </p>
      </div>
    </div>

    <!-- FM3 Profiles Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">FM3 Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >-</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-gray-500 dark:text-gray-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z"
              ></path>
            </svg>
            Scanning...
          </span>
        </p>
      </div>
    </div>

    <!-- System Status Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">System Status</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >Ready</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            All systems operational
          </span>
        </p>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 2xl:grid-cols-2 xl:gap-4 my-4">
    <!-- Browser Profiles Section - Placeholder for future implementation -->
    <div class="mb-4 col-span-full xl:mb-2">
      <div
        class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex-shrink-0">
            <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white"
              >Browser Profiles</span
            >
            <h3 class="text-base font-light text-gray-500 dark:text-gray-400">
              Manage your browser profiles for automation projects
            </h3>
          </div>
          <div
            class="flex items-center justify-end flex-1 text-base font-medium text-blue-600 dark:text-blue-500"
          >
            Manage Profiles
            <svg
              class="w-5 h-5 ml-1"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
        <div class="flex flex-col mt-8">
          <div class="text-center py-12">
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No browser profiles
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating your first browser profile for automation.
            </p>
            <div class="mt-6">
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Profile creation will be available in future releases.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Future Features Section -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
      <!-- Placeholder for future browser profile features -->
    </div>
  </div>
</template>
