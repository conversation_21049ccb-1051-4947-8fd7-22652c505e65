<script setup lang="ts">
import {
  <PERSON>wb<PERSON><PERSON><PERSON>,
  Fwb<PERSON><PERSON>ge,
  Fwb<PERSON><PERSON>,
  FwbTableBody,
  FwbTableCell,
  FwbTableHead,
  FwbTableHeadCell,
  FwbTableRow,
} from 'flowbite-vue'
</script>

<template>
  <!-- <PERSON> Header -->
  <div
    class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="w-full mb-1">
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a
                href="#"
                class="inline-flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white"
              >
                <svg
                  class="w-5 h-5 mr-2.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                  ></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page"
                  >Dashboard</span
                >
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Ghost Automator Pro Dashboard
        </h1>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-4">
    <!-- Total Posts Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Posts</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >2,340</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            12.5%
          </span>
          Since last month
        </p>
      </div>
      <div class="w-full max-w-none">
        <div id="main-chart"></div>
      </div>
    </div>

    <!-- Automated Posts Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Automated Posts</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >1,890</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            24.1%
          </span>
          Since last month
        </p>
      </div>
    </div>

    <!-- Active Rules Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Rules</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >47</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z"
              ></path>
            </svg>
            -3.2%
          </span>
          Since last month
        </p>
      </div>
    </div>

    <!-- Page Views Card -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Page Views</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >64.3K</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            18.7%
          </span>
          Since last month
        </p>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 2xl:grid-cols-2 xl:gap-4 my-4">
    <!-- Recent Posts Table -->
    <div class="mb-4 col-span-full xl:mb-2">
      <div
        class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex-shrink-0">
            <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white"
              >Recent Posts</span
            >
            <h3 class="text-base font-light text-gray-500 dark:text-gray-400">
              Latest published content from your Ghost CMS
            </h3>
          </div>
          <div
            class="flex items-center justify-end flex-1 text-base font-medium text-blue-600 dark:text-blue-500"
          >
            View all
            <svg
              class="w-5 h-5 ml-1"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
        <div class="flex flex-col mt-8">
          <div class="overflow-x-auto rounded-lg">
            <div class="inline-block min-w-full align-middle">
              <div class="overflow-hidden shadow sm:rounded-lg">
                <FwbTable>
                  <FwbTableHead class="bg-gray-50 dark:bg-gray-700">
                    <FwbTableHeadCell>Post Title</FwbTableHeadCell>
                    <FwbTableHeadCell>Author</FwbTableHeadCell>
                    <FwbTableHeadCell>Status</FwbTableHeadCell>
                    <FwbTableHeadCell>Views</FwbTableHeadCell>
                    <FwbTableHeadCell>Published</FwbTableHeadCell>
                    <FwbTableHeadCell>Actions</FwbTableHeadCell>
                  </FwbTableHead>
                  <FwbTableBody class="bg-white dark:bg-gray-800">
                    <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        Advanced Ghost Automation Techniques
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        John Doe
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        <div class="flex items-center">
                          <div class="h-2.5 w-2.5 rounded-full bg-green-400 mr-2"></div>
                          Published
                        </div>
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        2,847
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        2 hours ago
                      </FwbTableCell>
                      <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                        <FwbButton size="sm" color="blue">Edit</FwbButton>
                        <FwbButton size="sm" color="red">Delete</FwbButton>
                      </FwbTableCell>
                    </FwbTableRow>
                    <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        SEO Best Practices for 2024
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        Jane Smith
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        <div class="flex items-center">
                          <div class="h-2.5 w-2.5 rounded-full bg-green-400 mr-2"></div>
                          Published
                        </div>
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        1,924
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        1 day ago
                      </FwbTableCell>
                      <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                        <FwbButton size="sm" color="blue">Edit</FwbButton>
                        <FwbButton size="sm" color="red">Delete</FwbButton>
                      </FwbTableCell>
                    </FwbTableRow>
                    <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        Content Marketing Strategy Guide
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        Mike Johnson
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-normal text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        <div class="flex items-center">
                          <div class="h-2.5 w-2.5 rounded-full bg-yellow-400 mr-2"></div>
                          Draft
                        </div>
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        -
                      </FwbTableCell>
                      <FwbTableCell
                        class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        3 days ago
                      </FwbTableCell>
                      <FwbTableCell class="p-4 space-x-2 whitespace-nowrap">
                        <FwbButton size="sm" color="blue">Edit</FwbButton>
                        <FwbButton size="sm" color="red">Delete</FwbButton>
                      </FwbTableCell>
                    </FwbTableRow>
                  </FwbTableBody>
                </FwbTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Automation Rules & Activity -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
      <!-- Active Automation Rules -->
      <div
        class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex-shrink-0">
            <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white"
              >Automation Rules</span
            >
            <h3 class="text-base font-light text-gray-500 dark:text-gray-400">
              Active automation workflows
            </h3>
          </div>
          <div
            class="flex items-center justify-end flex-1 text-base font-medium text-blue-600 dark:text-blue-500"
          >
            Manage
            <svg
              class="w-5 h-5 ml-1"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
        <div class="space-y-4">
          <!-- Rule 1 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-10 h-10 bg-green-100 rounded-lg dark:bg-green-900 flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-green-600 dark:text-green-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                  Auto-publish scheduled posts
                </p>
                <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                  Runs daily at 9:00 AM
                </p>
              </div>
            </div>
            <div
              class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white"
            >
              <FwbBadge type="green" size="sm">Active</FwbBadge>
            </div>
          </div>

          <!-- Rule 2 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-10 h-10 bg-blue-100 rounded-lg dark:bg-blue-900 flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-blue-600 dark:text-blue-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
                    ></path>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                  Email newsletter automation
                </p>
                <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                  Weekly digest on Sundays
                </p>
              </div>
            </div>
            <div
              class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white"
            >
              <FwbBadge type="green" size="sm">Active</FwbBadge>
            </div>
          </div>

          <!-- Rule 3 -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-10 h-10 bg-yellow-100 rounded-lg dark:bg-yellow-900 flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-yellow-600 dark:text-yellow-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                  SEO optimization check
                </p>
                <p class="text-sm text-gray-500 truncate dark:text-gray-400">Needs configuration</p>
              </div>
            </div>
            <div
              class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white"
            >
              <FwbBadge type="yellow" size="sm">Paused</FwbBadge>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
