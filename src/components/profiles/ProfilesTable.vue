<script setup lang="ts">
import {
  FwbBadge,
  FwbTable,
  FwbTableBody,
  FwbTableCell,
  FwbTableHead,
  FwbTableHeadCell,
  FwbTableRow,
} from 'flowbite-vue'

interface BrowserProfile {
  id: string
  name: string
  browserType: string
  version: string
  proxy: string
  created: string
  lastUsed: string
  status: 'Active' | 'Inactive' | 'Idle'
  tags: string
}

defineProps<{
  browserProfiles: BrowserProfile[]
}>()

const getBrowserColor = (browserType: string) => {
  const colors = {
    Chrome: 'bg-blue-500',
    Edge: 'bg-blue-500',
    Opera: 'bg-red-500',
    Firefox: 'bg-orange-500',
  }
  return colors[browserType] || 'bg-gray-500'
}

const getStatusBadgeType = (status: string) => {
  const types = {
    Active: 'green',
    Inactive: 'default',
    Idle: 'red',
  }
  return types[status] || 'default'
}
</script>

<template>
  <div class="flex flex-col mt-6">
    <div class="overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden shadow">
          <FwbTable>
            <FwbTableHead class="bg-gray-50 dark:bg-gray-700">
              <FwbTableHeadCell class="w-4">
                <label for="checkbox-all" class="sr-only">checkbox</label>
                <input
                  id="checkbox-all"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">NAME</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">BROWSER TYPE</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">VERSION</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">PROXY</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">CREATED</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">LAST USED</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">STATUS</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">TAGS</FwbTableHeadCell>
              <FwbTableHeadCell class="text-right">ACTIONS</FwbTableHeadCell>
            </FwbTableHead>
            <FwbTableBody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <FwbTableRow
                v-for="browserProfile in browserProfiles"
                :key="browserProfile.id"
                class="hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <FwbTableCell class="w-4 p-4">
                  <input
                    :id="`checkbox-${browserProfile.id}`"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                  {{ browserProfile.name }}
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center">
                    <div :class="getBrowserColor(browserProfile.browserType)" class="w-4 h-4 rounded-full mr-2"></div>
                    {{ browserProfile.browserType }}
                  </div>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  {{ browserProfile.version }}
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  {{ browserProfile.proxy }}
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  {{ browserProfile.created }}
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  {{ browserProfile.lastUsed }}
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4">
                  <FwbBadge :type="getStatusBadgeType(browserProfile.status)" size="sm">
                    {{ browserProfile.status }}
                  </FwbBadge>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                    {{ browserProfile.tags }}
                  </span>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path d="M10 4a2 2 0 100-4 2 2 0 000 4z"/>
                        <path d="M10 20a2 2 0 100-4 2 2 0 000 4z"/>
                      </svg>
                    </button>
                  </div>
                </FwbTableCell>
              </FwbTableRow>
            </FwbTableBody>
          </FwbTable>
        </div>
      </div>
    </div>
  </div>
</template>
