<script setup lang="ts">
interface Props {
  title: string
  value: string | number
  subtitle?: string
  color?: 'green' | 'blue' | 'yellow' | 'red' | 'gray'
  showProgressBar?: boolean
  progressValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue',
  showProgressBar: false,
  progressValue: 0,
})

const getColorClasses = (color: string, progressValue?: number) => {
  const baseColors = {
    green: 'text-green-600 dark:text-green-400',
    blue: 'text-blue-600 dark:text-blue-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
    red: 'text-red-600 dark:text-red-400',
    gray: 'text-gray-600 dark:text-gray-400',
  }

  const progressColors = {
    green: 'bg-green-500',
    blue: 'bg-blue-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
    gray: 'bg-gray-500',
  }

  // Dynamic color based on progress value
  if (progressValue !== undefined) {
    if (progressValue < 50) return { text: baseColors.green, progress: progressColors.green }
    if (progressValue < 75) return { text: baseColors.yellow, progress: progressColors.yellow }
    return { text: baseColors.red, progress: progressColors.red }
  }

  return { text: baseColors[color], progress: progressColors[color] }
}

const colorClasses = getColorClasses(props.color, props.progressValue)
</script>

<template>
  <div
    class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="w-full">
      <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">{{ title }}</h3>
      <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">
        {{ value }}
      </span>
      
      <!-- Progress Bar -->
      <div v-if="showProgressBar" class="w-full bg-gray-200 rounded-full h-2.5 mt-2 dark:bg-gray-700">
        <div
          :class="colorClasses.progress"
          class="h-2.5 rounded-full transition-all duration-300"
          :style="{ width: progressValue + '%' }"
        ></div>
      </div>
      
      <!-- Subtitle -->
      <p v-if="subtitle" class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400 mt-2">
        <span :class="colorClasses.text" class="flex items-center mr-1.5 text-sm">
          <svg
            class="w-4 h-4"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd"
            ></path>
          </svg>
          {{ subtitle }}
        </span>
      </p>
    </div>
  </div>
</template>
