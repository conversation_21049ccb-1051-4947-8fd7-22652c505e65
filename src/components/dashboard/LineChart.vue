<script setup lang="ts">
import { FwbBadge } from 'flowbite-vue'

interface Props {
  title: string
  data: number[]
  color?: string
  gradientId?: string
  badgeText?: string
  badgeType?: 'green' | 'blue' | 'yellow' | 'red' | 'default'
}

const props = withDefaults(defineProps<Props>(), {
  color: '#3b82f6',
  gradientId: 'defaultGradient',
  badgeText: 'Live',
  badgeType: 'green',
})

// Generate SVG path points from data
const generatePath = (data: number[], width = 400, height = 200) => {
  const points = data.map((value, index) => {
    const x = (index * width) / (data.length - 1)
    const y = height - (value * height) / 100 // Assuming values are 0-100
    return `${x},${y}`
  })
  return points.join(' ')
}

// Generate area path for gradient fill
const generateAreaPath = (data: number[], width = 400, height = 200) => {
  const points = data.map((value, index) => {
    const x = (index * width) / (data.length - 1)
    const y = height - (value * height) / 100
    return `${x},${y}`
  })
  
  // Create closed path for area
  const firstPoint = `0,${height}`
  const lastPoint = `${width},${height}`
  return `M${firstPoint} L${points.join(' L')} L${lastPoint} Z`
}

const pathPoints = generatePath(props.data)
const areaPath = generateAreaPath(props.data)
</script>

<template>
  <div
    class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800"
  >
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ title }}</h3>
      <FwbBadge :type="badgeType" size="sm">{{ badgeText }}</FwbBadge>
    </div>
    <div class="relative h-64">
      <!-- Simple SVG Chart -->
      <svg class="w-full h-full" viewBox="0 0 400 200">
        <defs>
          <linearGradient :id="gradientId" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" :style="`stop-color:${color};stop-opacity:0.3`" />
            <stop offset="100%" :style="`stop-color:${color};stop-opacity:0`" />
          </linearGradient>
        </defs>
        
        <!-- Grid lines -->
        <g stroke="#e5e7eb" stroke-width="1" opacity="0.3">
          <line x1="0" y1="50" x2="400" y2="50" />
          <line x1="0" y1="100" x2="400" y2="100" />
          <line x1="0" y1="150" x2="400" y2="150" />
        </g>
        
        <!-- Area fill -->
        <path
          :d="areaPath"
          :fill="`url(#${gradientId})`"
        />
        
        <!-- Chart line -->
        <polyline
          :points="pathPoints"
          fill="none"
          :stroke="color"
          stroke-width="2"
        />
        
        <!-- Data points -->
        <g v-for="(value, index) in data" :key="index">
          <circle
            :cx="(index * 400) / (data.length - 1)"
            :cy="200 - (value * 200) / 100"
            r="3"
            :fill="color"
            class="opacity-0 hover:opacity-100 transition-opacity duration-200"
          />
        </g>
      </svg>
    </div>
  </div>
</template>
