<script setup lang="ts">
import { FwbDropdown, FwbAvatar } from 'flowbite-vue'

defineProps<{
  sidebarOpen: boolean
  isDarkMode: boolean
}>()

defineEmits<{
  toggleSidebar: []
  toggleTheme: []
}>()
</script>

<template>
  <!-- Header -->
  <nav
    class="fixed top-0 z-50 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="px-3 py-3 lg:px-5 lg:pl-3">
      <div class="flex items-center justify-between">
        <!-- Header Left Side -->
        <div class="flex items-center justify-start rtl:justify-end">
          <!-- Sidebar Toggle Button -->
          <button
            @click="$emit('toggleSidebar')"
            type="button"
            class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg sm:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
          >
            <span class="sr-only">Open sidebar</span>
            <svg
              class="w-6 h-6"
              aria-hidden="true"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z"
              ></path>
            </svg>
          </button>

          <!-- Logo -->
          <RouterLink to="/" class="flex ml-2 md:mr-24">
            <img
              src="https://flowbite.com/docs/images/logo.svg"
              class="h-8 mr-3"
              alt="Ghost Automator Pro Logo"
            />
            <span
              class="self-center text-xl font-semibold sm:text-2xl whitespace-nowrap dark:text-white"
              >Ghost Automator Pro</span
            >
          </RouterLink>
        </div>

        <!-- Header Center - Reserved for future features -->
        <div class="flex items-center lg:order-1">
          <!-- Reserved space for search functionality -->
        </div>

        <!-- Header Right Side -->
        <div class="flex items-center lg:order-2">
          <!-- Theme Toggle -->
          <button
            @click="$emit('toggleTheme')"
            type="button"
            class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 mr-3"
          >
            <svg
              v-if="!isDarkMode"
              class="w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
              ></path>
            </svg>
            <svg
              v-else
              class="w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="sr-only">Toggle dark mode</span>
          </button>

          <!-- User Menu -->
          <div class="flex items-center ml-3">
            <FwbDropdown placement="bottom-end">
              <template #trigger>
                <button
                  type="button"
                  class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
                >
                  <span class="sr-only">Open user menu</span>
                  <FwbAvatar
                    img="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
                    rounded
                    size="sm"
                  />
                </button>
              </template>
              <template #header>
                <div class="px-4 py-3">
                  <p class="text-sm text-gray-900 dark:text-white">Neil Sims</p>
                  <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300">
                    <EMAIL>
                  </p>
                </div>
              </template>
              <ul class="py-1">
                <li>
                  <RouterLink
                    to="/"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                    >Dashboard</RouterLink
                  >
                </li>
                <li>
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                    >Settings</a
                  >
                </li>
                <li>
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                    >Sign out</a
                  >
                </li>
              </ul>
            </FwbDropdown>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>
