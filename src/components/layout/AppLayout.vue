<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import AppFooter from './AppFooter.vue'

// Sidebar state
const sidebarOpen = ref(false)

// Theme state
const isDarkMode = ref(false)

// Initialize theme from localStorage
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
    isDarkMode.value = true
    document.documentElement.classList.add('dark')
  } else {
    isDarkMode.value = false
    document.documentElement.classList.remove('dark')
  }
})

// Toggle sidebar
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

// Close sidebar
const closeSidebar = () => {
  sidebarOpen.value = false
}

// Toggle theme
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('theme', 'light')
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <AppHeader
      :sidebar-open="sidebarOpen"
      :is-dark-mode="isDarkMode"
      @toggle-sidebar="toggleSidebar"
      @toggle-theme="toggleTheme"
    />

    <!-- Sidebar -->
    <AppSidebar
      :sidebar-open="sidebarOpen"
      @close-sidebar="closeSidebar"
    />

    <!-- Main Content -->
    <div class="p-4 sm:ml-64">
      <div class="p-4 border-2 border-gray-200 border-dashed rounded-lg dark:border-gray-700 mt-14">
        <RouterView />
      </div>
    </div>

    <!-- Footer -->
    <AppFooter />

    <!-- Mobile sidebar backdrop -->
    <div
      v-if="sidebarOpen"
      class="fixed inset-0 z-30 bg-gray-900 bg-opacity-50 sm:hidden"
      @click="closeSidebar"
    ></div>
  </div>
</template>
