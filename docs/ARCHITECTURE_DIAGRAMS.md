# Architecture Diagrams for Ghost Automator Pro

This document contains comprehensive architecture diagrams for the Ghost Automator Pro application, showing the relationships between different components and systems.

## Table of Contents

- [Application Architecture](#application-architecture)
- [Electron Process Architecture](#electron-process-architecture)
- [Browser Profile Management Workflow](#browser-profile-management-workflow)
- [Task Automation Workflow](#task-automation-workflow)
- [IPC Communication Flow](#ipc-communication-flow)
- [Data Flow Architecture](#data-flow-architecture)

## Application Architecture

This diagram shows the overall architecture of the Ghost Automator Pro application:

```mermaid
graph TB
    subgraph "Electron Application"
        subgraph "Main Process"
            MP[Main Process]
            IPC[IPC Handlers]
            CONFIG[App Configuration]
            PLAYWRIGHT[Playwright Integration]
            FS[File System Operations]
        end
        
        subgraph "Preload Script"
            PRELOAD[Preload Script]
            API[Exposed APIs]
        end
        
        subgraph "Renderer Process"
            subgraph "Vue 3 Application"
                ROUTER[Vue Router]
                PINIA[Pinia Store]
                COMPONENTS[Vue Components]
                LAYOUTS[Layouts]
            end
            
            subgraph "UI Framework"
                FLOWBITE[Flowbite Vue]
                TAILWIND[Tailwind CSS]
                THEME[Theme System]
            end
        end
    end
    
    subgraph "External Systems"
        BROWSER[Browser Instances]
        FINGERPRINT[Fingerprint Service]
        FILESYSTEM[File System]
        OS[Operating System]
    end
    
    MP --> PRELOAD
    PRELOAD --> COMPONENTS
    IPC --> MP
    COMPONENTS --> API
    API --> IPC
    
    MP --> BROWSER
    MP --> FINGERPRINT
    MP --> FILESYSTEM
    MP --> OS
    
    COMPONENTS --> FLOWBITE
    FLOWBITE --> TAILWIND
    COMPONENTS --> ROUTER
    COMPONENTS --> PINIA
    
    CONFIG --> MP
    PLAYWRIGHT --> BROWSER
```

## Electron Process Architecture

This diagram details the Electron process structure and communication:

```mermaid
graph LR
    subgraph "Main Process"
        MAIN[index.ts]
        WINDOW[BrowserWindow]
        HANDLERS[IPC Handlers]
        SERVICES[Services]
        CONFIG[AppConfig]
    end
    
    subgraph "Preload Script"
        PRELOAD[index.ts]
        CONTEXT[Context Bridge]
        TYPES[Type Definitions]
    end
    
    subgraph "Renderer Process"
        VUE[Vue Application]
        ELECTRON_API[Electron API]
        COMPONENTS[Components]
    end
    
    subgraph "External Resources"
        BROWSERS[Browser Processes]
        FILES[File System]
        NOTIFICATIONS[System Notifications]
    end
    
    MAIN --> WINDOW
    MAIN --> HANDLERS
    MAIN --> SERVICES
    CONFIG --> MAIN
    
    WINDOW --> PRELOAD
    PRELOAD --> CONTEXT
    CONTEXT --> TYPES
    
    CONTEXT --> ELECTRON_API
    ELECTRON_API --> VUE
    VUE --> COMPONENTS
    
    HANDLERS --> BROWSERS
    HANDLERS --> FILES
    HANDLERS --> NOTIFICATIONS
    
    COMPONENTS -.->|IPC| HANDLERS
```

## Browser Profile Management Workflow

This diagram shows the complete workflow for managing browser profiles:

```mermaid
flowchart TD
    START([User Starts Profile Creation])
    
    subgraph "Profile Configuration"
        NAME[Enter Profile Name]
        TARGET[Select Target Websites]
        BROWSER[Choose Browser Type]
        FINGERPRINT_CONFIG[Configure Fingerprint Parameters]
    end
    
    subgraph "Fingerprint Generation"
        REQUEST[Request Fingerprint from Service]
        RECEIVE[Receive Fingerprint Data]
        VALIDATE[Validate Fingerprint]
    end
    
    subgraph "Proxy Configuration"
        PROXY_ENABLE[Enable Proxy?]
        PROXY_SETTINGS[Configure Proxy Settings]
        PROXY_TEST[Test Proxy Connection]
    end
    
    subgraph "Extension Management"
        EXT_SELECT[Select Extensions]
        EXT_CONFIG[Configure Extension Settings]
    end
    
    subgraph "Profile Storage"
        SAVE[Save Profile Configuration]
        VALIDATE_PROFILE[Validate Complete Profile]
        STORE[Store in File System]
    end
    
    TEST[Test Profile Launch]
    SUCCESS([Profile Created Successfully])
    ERROR([Error - Show Message])
    
    START --> NAME
    NAME --> TARGET
    TARGET --> BROWSER
    BROWSER --> FINGERPRINT_CONFIG
    
    FINGERPRINT_CONFIG --> REQUEST
    REQUEST --> RECEIVE
    RECEIVE --> VALIDATE
    VALIDATE -->|Valid| PROXY_ENABLE
    VALIDATE -->|Invalid| ERROR
    
    PROXY_ENABLE -->|Yes| PROXY_SETTINGS
    PROXY_ENABLE -->|No| EXT_SELECT
    PROXY_SETTINGS --> PROXY_TEST
    PROXY_TEST -->|Success| EXT_SELECT
    PROXY_TEST -->|Failed| ERROR
    
    EXT_SELECT --> EXT_CONFIG
    EXT_CONFIG --> SAVE
    
    SAVE --> VALIDATE_PROFILE
    VALIDATE_PROFILE -->|Valid| STORE
    VALIDATE_PROFILE -->|Invalid| ERROR
    
    STORE --> TEST
    TEST -->|Success| SUCCESS
    TEST -->|Failed| ERROR
```

## Task Automation Workflow

This diagram illustrates the task automation and browser preparation workflow:

```mermaid
flowchart TD
    START([User Creates Task])
    
    subgraph "Task Configuration"
        TASK_NAME[Enter Task Name]
        SELECT_PROFILES[Select Browser Profiles]
        TASK_TYPE[Choose Task Type]
        KEYWORDS[Configure Keywords/Parameters]
    end
    
    subgraph "Task Execution"
        VALIDATE_PROFILES[Validate Selected Profiles]
        LAUNCH_BROWSERS[Launch Browser Instances]
        APPLY_FINGERPRINTS[Apply Fingerprints]
        CONFIGURE_PROXIES[Configure Proxies]
    end
    
    subgraph "Browser Automation"
        NAVIGATE[Navigate to Target Sites]
        PERFORM_SEARCHES[Perform Google Searches]
        CLICK_LINKS[Click Search Results]
        BROWSE_PAGES[Browse Pages Naturally]
        COLLECT_DATA[Collect Cookies/History]
    end
    
    subgraph "Monitoring"
        TRACK_PROGRESS[Track Task Progress]
        CAPTURE_SCREENSHOTS[Capture Screenshots]
        LOG_ACTIVITIES[Log Activities]
        HANDLE_ERRORS[Handle Errors]
    end
    
    subgraph "Completion"
        SAVE_RESULTS[Save Browsing Results]
        UPDATE_PROFILES[Update Profile Data]
        CLEANUP[Cleanup Resources]
        NOTIFY[Notify User]
    end
    
    ERROR([Error Handling])
    SUCCESS([Task Completed])
    
    START --> TASK_NAME
    TASK_NAME --> SELECT_PROFILES
    SELECT_PROFILES --> TASK_TYPE
    TASK_TYPE --> KEYWORDS
    
    KEYWORDS --> VALIDATE_PROFILES
    VALIDATE_PROFILES -->|Valid| LAUNCH_BROWSERS
    VALIDATE_PROFILES -->|Invalid| ERROR
    
    LAUNCH_BROWSERS --> APPLY_FINGERPRINTS
    APPLY_FINGERPRINTS --> CONFIGURE_PROXIES
    CONFIGURE_PROXIES --> NAVIGATE
    
    NAVIGATE --> PERFORM_SEARCHES
    PERFORM_SEARCHES --> CLICK_LINKS
    CLICK_LINKS --> BROWSE_PAGES
    BROWSE_PAGES --> COLLECT_DATA
    
    COLLECT_DATA --> TRACK_PROGRESS
    TRACK_PROGRESS --> CAPTURE_SCREENSHOTS
    CAPTURE_SCREENSHOTS --> LOG_ACTIVITIES
    LOG_ACTIVITIES --> HANDLE_ERRORS
    
    HANDLE_ERRORS -->|Continue| PERFORM_SEARCHES
    HANDLE_ERRORS -->|Complete| SAVE_RESULTS
    
    SAVE_RESULTS --> UPDATE_PROFILES
    UPDATE_PROFILES --> CLEANUP
    CLEANUP --> NOTIFY
    NOTIFY --> SUCCESS
    
    HANDLE_ERRORS -->|Critical Error| ERROR
```

## IPC Communication Flow

This diagram shows the Inter-Process Communication flow between main and renderer processes:

```mermaid
sequenceDiagram
    participant R as Renderer Process
    participant P as Preload Script
    participant M as Main Process
    participant B as Browser Instance
    participant F as File System
    
    Note over R,F: Profile Creation Flow
    
    R->>P: createProfile(profileData)
    P->>M: IPC: create-profile
    M->>F: Save profile to disk
    F-->>M: Success/Error
    M-->>P: Response
    P-->>R: Profile created
    
    Note over R,F: Browser Launch Flow
    
    R->>P: launchBrowser(profileId)
    P->>M: IPC: launch-browser
    M->>F: Load profile data
    F-->>M: Profile data
    M->>B: Launch with fingerprint
    B-->>M: Browser instance
    M-->>P: Success + process ID
    P-->>R: Browser launched
    
    Note over R,F: File Operations
    
    R->>P: exportProfiles(profiles)
    P->>M: IPC: export-profiles
    M->>F: Show save dialog
    F-->>M: File path
    M->>F: Write profiles to file
    F-->>M: Success
    M-->>P: Export complete
    P-->>R: File saved
    
    Note over R,F: System Integration
    
    R->>P: showNotification(title, body)
    P->>M: IPC: show-notification
    M->>M: Create notification
    M-->>P: Notification shown
    P-->>R: Success
```

## Data Flow Architecture

This diagram shows how data flows through the application:

```mermaid
graph TD
    subgraph "Data Sources"
        CONFIG_FILES[Configuration Files]
        PROFILE_FILES[Profile Files]
        FINGERPRINT_API[Fingerprint Service API]
        BROWSER_DATA[Browser Generated Data]
    end
    
    subgraph "Data Processing Layer"
        CONFIG_SERVICE[Configuration Service]
        PROFILE_SERVICE[Profile Service]
        FINGERPRINT_SERVICE[Fingerprint Service]
        AUTOMATION_SERVICE[Automation Service]
    end
    
    subgraph "State Management"
        PINIA_STORE[Pinia Store]
        LOCAL_STATE[Component State]
        PERSISTENT_STATE[Persistent Storage]
    end
    
    subgraph "UI Components"
        DASHBOARD[Dashboard]
        PROFILE_MANAGER[Profile Manager]
        TASK_MANAGER[Task Manager]
        SETTINGS[Settings]
    end
    
    subgraph "Output"
        BROWSER_INSTANCES[Browser Instances]
        EXPORTED_FILES[Exported Files]
        LOGS[Application Logs]
        NOTIFICATIONS[User Notifications]
    end
    
    CONFIG_FILES --> CONFIG_SERVICE
    PROFILE_FILES --> PROFILE_SERVICE
    FINGERPRINT_API --> FINGERPRINT_SERVICE
    BROWSER_DATA --> AUTOMATION_SERVICE
    
    CONFIG_SERVICE --> PINIA_STORE
    PROFILE_SERVICE --> PINIA_STORE
    FINGERPRINT_SERVICE --> PINIA_STORE
    AUTOMATION_SERVICE --> PINIA_STORE
    
    PINIA_STORE --> LOCAL_STATE
    LOCAL_STATE --> PERSISTENT_STATE
    
    PINIA_STORE --> DASHBOARD
    PINIA_STORE --> PROFILE_MANAGER
    PINIA_STORE --> TASK_MANAGER
    PINIA_STORE --> SETTINGS
    
    PROFILE_MANAGER --> BROWSER_INSTANCES
    TASK_MANAGER --> BROWSER_INSTANCES
    PROFILE_SERVICE --> EXPORTED_FILES
    AUTOMATION_SERVICE --> LOGS
    CONFIG_SERVICE --> NOTIFICATIONS
```

These diagrams provide a comprehensive view of the Ghost Automator Pro architecture, showing how different components interact and data flows through the system. They serve as a reference for developers working on the application and help understand the overall system design.
