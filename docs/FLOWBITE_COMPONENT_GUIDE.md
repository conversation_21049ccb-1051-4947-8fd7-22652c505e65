# Flowbite Vue Component Guide for Ghost Automator Pro

This guide provides comprehensive instructions for using Flowbite Vue components to build the Ghost Automator Pro user interface, replacing Vuetify components with Flowbite equivalents.

## Table of Contents

- [Overview](#overview)
- [Installation and Setup](#installation-and-setup)
- [Component Migration Map](#component-migration-map)
- [Layout Components](#layout-components)
- [Data Display Components](#data-display-components)
- [Form Components](#form-components)
- [Navigation Components](#navigation-components)
- [Feedback Components](#feedback-components)
- [Theme and Dark Mode](#theme-and-dark-mode)

## Overview

Flowbite Vue provides a comprehensive set of components built on top of Tailwind CSS, offering:

- **Modern Design**: Clean, professional components following modern design principles
- **Dark Mode Support**: Built-in dark/light theme switching
- **Accessibility**: WCAG compliant components with proper ARIA attributes
- **TypeScript Support**: Full TypeScript definitions for all components
- **Tailwind Integration**: Seamless integration with Tailwind CSS utilities

## Installation and Setup

### 1. Dependencies

The project already includes the necessary dependencies:

```json
{
  "dependencies": {
    "flowbite": "3.1.2",
    "flowbite-vue": "0.2.1"
  },
  "devDependencies": {
    "@tailwindcss/vite": "4.1.8",
    "tailwindcss": "4.1.8"
  }
}
```

### 2. Tailwind Configuration

Ensure your `tailwind.config.js` includes Flowbite:

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "./node_modules/flowbite/**/*.js"
  ],
  darkMode: 'class',
  theme: {
    extend: {},
  },
  plugins: [
    require('flowbite/plugin')
  ],
}
```

### 3. CSS Import

In your `src/assets/main.css`:

```css
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import 'flowbite';
```

## Component Migration Map

### Vuetify → Flowbite Vue Equivalents

| Vuetify Component | Flowbite Vue Component | Usage |
|-------------------|------------------------|-------|
| `v-app` | `div` with Tailwind classes | Application wrapper |
| `v-app-bar` | `FwbNavbar` | Top navigation bar |
| `v-navigation-drawer` | `FwbSidebar` | Side navigation |
| `v-main` | `div` with Tailwind classes | Main content area |
| `v-container` | `div` with container classes | Content container |
| `v-card` | `FwbCard` | Card component |
| `v-btn` | `FwbButton` | Button component |
| `v-data-table` | `FwbTable` | Data table |
| `v-text-field` | `FwbInput` | Text input |
| `v-select` | `FwbSelect` | Select dropdown |
| `v-checkbox` | `FwbCheckbox` | Checkbox input |
| `v-radio` | `FwbRadio` | Radio button |
| `v-dialog` | `FwbModal` | Modal dialog |
| `v-alert` | `FwbAlert` | Alert message |
| `v-progress-linear` | `FwbProgress` | Progress bar |
| `v-badge` | `FwbBadge` | Badge component |
| `v-chip` | `FwbBadge` | Chip/tag component |
| `v-tabs` | `FwbTabs` | Tab navigation |
| `v-menu` | `FwbDropdown` | Dropdown menu |
| `v-tooltip` | `FwbTooltip` | Tooltip |

## Layout Components

### Application Layout

```vue
<template>
  <div class="antialiased bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <FwbNavbar>
      <template #logo>
        <FwbNavbarLogo
          alt="Ghost Automator Pro"
          image-url="/logo.svg"
          link="/"
        >
          Ghost Automator Pro
        </FwbNavbarLogo>
      </template>
      
      <template #default="{ isShowMenu }">
        <FwbNavbarCollapse :is-show-menu="isShowMenu">
          <FwbNavbarLink href="/" :is-active="$route.path === '/'">
            Dashboard
          </FwbNavbarLink>
          <FwbNavbarLink href="/profiles" :is-active="$route.path === '/profiles'">
            Browser Profiles
          </FwbNavbarLink>
          <FwbNavbarLink href="/tasks" :is-active="$route.path === '/tasks'">
            Active Tasks
          </FwbNavbarLink>
        </FwbNavbarCollapse>
      </template>
    </FwbNavbar>

    <!-- Sidebar -->
    <FwbSidebar>
      <FwbSidebarGroup>
        <FwbSidebarItem href="/" :is-active="$route.path === '/'">
          <template #icon>
            <DashboardIcon />
          </template>
          Dashboard
        </FwbSidebarItem>
        <FwbSidebarItem href="/profiles" :is-active="$route.path === '/profiles'">
          <template #icon>
            <ProfileIcon />
          </template>
          Browser Profiles
        </FwbSidebarItem>
      </FwbSidebarGroup>
    </FwbSidebar>

    <!-- Main Content -->
    <div class="p-4 sm:ml-64">
      <div class="p-4 mt-14">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  FwbNavbar,
  FwbNavbarLogo,
  FwbNavbarCollapse,
  FwbNavbarLink,
  FwbSidebar,
  FwbSidebarGroup,
  FwbSidebarItem
} from 'flowbite-vue'
</script>
```

### Card Layout

```vue
<template>
  <FwbCard>
    <template #header>
      <h3 class="text-xl font-bold text-gray-900 dark:text-white">
        Browser Profiles
      </h3>
    </template>
    
    <div class="space-y-4">
      <!-- Card content -->
    </div>
    
    <template #footer>
      <div class="flex justify-between">
        <FwbButton color="alternative">Cancel</FwbButton>
        <FwbButton color="blue">Save</FwbButton>
      </div>
    </template>
  </FwbCard>
</template>

<script setup lang="ts">
import { FwbCard, FwbButton } from 'flowbite-vue'
</script>
```

## Data Display Components

### Data Table

```vue
<template>
  <FwbTable>
    <FwbTableHead>
      <FwbTableHeadCell>Profile Name</FwbTableHeadCell>
      <FwbTableHeadCell>Browser Type</FwbTableHeadCell>
      <FwbTableHeadCell>Status</FwbTableHeadCell>
      <FwbTableHeadCell>Actions</FwbTableHeadCell>
    </FwbTableHead>
    <FwbTableBody>
      <FwbTableRow v-for="profile in profiles" :key="profile.id">
        <FwbTableCell>{{ profile.name }}</FwbTableCell>
        <FwbTableCell>
          <FwbBadge :color="getBrowserColor(profile.browserType)">
            {{ profile.browserType }}
          </FwbBadge>
        </FwbTableCell>
        <FwbTableCell>
          <FwbBadge :color="getStatusColor(profile.status)">
            {{ profile.status }}
          </FwbBadge>
        </FwbTableCell>
        <FwbTableCell>
          <div class="flex space-x-2">
            <FwbButton size="xs" color="blue" @click="editProfile(profile)">
              Edit
            </FwbButton>
            <FwbButton size="xs" color="red" @click="deleteProfile(profile)">
              Delete
            </FwbButton>
          </div>
        </FwbTableCell>
      </FwbTableRow>
    </FwbTableBody>
  </FwbTable>
</template>

<script setup lang="ts">
import {
  FwbTable,
  FwbTableHead,
  FwbTableHeadCell,
  FwbTableBody,
  FwbTableRow,
  FwbTableCell,
  FwbBadge,
  FwbButton
} from 'flowbite-vue'

const profiles = ref([
  {
    id: 1,
    name: 'Profile 1',
    browserType: 'Chrome',
    status: 'Active'
  }
])

const getBrowserColor = (type: string) => {
  const colors = {
    'Chrome': 'blue',
    'Firefox': 'orange',
    'Edge': 'green'
  }
  return colors[type] || 'gray'
}

const getStatusColor = (status: string) => {
  const colors = {
    'Active': 'green',
    'Inactive': 'gray',
    'Error': 'red'
  }
  return colors[status] || 'gray'
}
</script>
```

### Statistics Cards

```vue
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <FwbCard v-for="stat in statistics" :key="stat.title">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div :class="`w-8 h-8 rounded-lg flex items-center justify-center ${stat.bgColor}`">
            <component :is="stat.icon" :class="`w-5 h-5 ${stat.iconColor}`" />
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
            {{ stat.title }}
          </p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ stat.value }}
          </p>
          <p :class="`text-sm ${stat.changeColor}`">
            {{ stat.change }}
          </p>
        </div>
      </div>
    </FwbCard>
  </div>
</template>

<script setup lang="ts">
import { FwbCard } from 'flowbite-vue'

const statistics = ref([
  {
    title: 'Active Profiles',
    value: '24',
    change: '+12% from last month',
    changeColor: 'text-green-600',
    bgColor: 'bg-blue-100 dark:bg-blue-900',
    iconColor: 'text-blue-600 dark:text-blue-400',
    icon: 'ProfileIcon'
  }
])
</script>
```

## Form Components

### Profile Creation Form

```vue
<template>
  <form @submit.prevent="submitForm" class="space-y-6">
    <!-- Profile Name -->
    <div>
      <FwbLabel for="profileName" value="Profile Name" />
      <FwbInput
        id="profileName"
        v-model="form.name"
        type="text"
        placeholder="Enter profile name"
        required
      />
    </div>

    <!-- Browser Type -->
    <div>
      <FwbLabel for="browserType" value="Browser Type" />
      <FwbSelect
        id="browserType"
        v-model="form.browserType"
        :options="browserOptions"
        placeholder="Select browser type"
      />
    </div>

    <!-- Proxy Settings -->
    <div>
      <FwbLabel value="Proxy Configuration" />
      <div class="space-y-4 mt-2">
        <FwbCheckbox
          v-model="form.useProxy"
          label="Enable Proxy"
        />
        
        <div v-if="form.useProxy" class="grid grid-cols-2 gap-4">
          <FwbInput
            v-model="form.proxyHost"
            placeholder="Proxy Host"
          />
          <FwbInput
            v-model="form.proxyPort"
            placeholder="Port"
            type="number"
          />
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-4">
      <FwbButton color="alternative" @click="$emit('cancel')">
        Cancel
      </FwbButton>
      <FwbButton color="blue" type="submit">
        Create Profile
      </FwbButton>
    </div>
  </form>
</template>

<script setup lang="ts">
import {
  FwbLabel,
  FwbInput,
  FwbSelect,
  FwbCheckbox,
  FwbButton
} from 'flowbite-vue'

const form = ref({
  name: '',
  browserType: '',
  useProxy: false,
  proxyHost: '',
  proxyPort: ''
})

const browserOptions = [
  { value: 'chrome', name: 'Google Chrome' },
  { value: 'firefox', name: 'Mozilla Firefox' },
  { value: 'edge', name: 'Microsoft Edge' }
]

const submitForm = () => {
  // Handle form submission
}
</script>
```

## Navigation Components

### Tabs Navigation

```vue
<template>
  <FwbTabs v-model="activeTab" class="mb-6">
    <FwbTab name="general" title="General Settings">
      <div class="p-4">
        <!-- General settings content -->
      </div>
    </FwbTab>
    
    <FwbTab name="fingerprint" title="Fingerprint">
      <div class="p-4">
        <!-- Fingerprint settings content -->
      </div>
    </FwbTab>
    
    <FwbTab name="proxy" title="Proxy">
      <div class="p-4">
        <!-- Proxy settings content -->
      </div>
    </FwbTab>
  </FwbTabs>
</template>

<script setup lang="ts">
import { FwbTabs, FwbTab } from 'flowbite-vue'

const activeTab = ref('general')
</script>
```

### Dropdown Menu

```vue
<template>
  <FwbDropdown text="Actions">
    <FwbListGroup>
      <FwbListGroupItem @click="editProfile">
        <template #prefix>
          <EditIcon class="w-4 h-4" />
        </template>
        Edit Profile
      </FwbListGroupItem>
      
      <FwbListGroupItem @click="duplicateProfile">
        <template #prefix>
          <CopyIcon class="w-4 h-4" />
        </template>
        Duplicate
      </FwbListGroupItem>
      
      <FwbListGroupItem @click="deleteProfile" class="text-red-600">
        <template #prefix>
          <DeleteIcon class="w-4 h-4" />
        </template>
        Delete
      </FwbListGroupItem>
    </FwbListGroup>
  </FwbDropdown>
</template>

<script setup lang="ts">
import {
  FwbDropdown,
  FwbListGroup,
  FwbListGroupItem
} from 'flowbite-vue'
</script>
```

## Feedback Components

### Alerts and Notifications

```vue
<template>
  <div class="space-y-4">
    <!-- Success Alert -->
    <FwbAlert
      v-if="showSuccess"
      type="success"
      closable
      @close="showSuccess = false"
    >
      <template #icon>
        <CheckIcon />
      </template>
      Profile created successfully!
    </FwbAlert>

    <!-- Error Alert -->
    <FwbAlert
      v-if="showError"
      type="danger"
      closable
      @close="showError = false"
    >
      <template #icon>
        <ErrorIcon />
      </template>
      Failed to create profile. Please try again.
    </FwbAlert>

    <!-- Info Alert -->
    <FwbAlert type="info">
      <template #icon>
        <InfoIcon />
      </template>
      <span class="font-medium">Info:</span> 
      This profile will use the default fingerprint settings.
    </FwbAlert>
  </div>
</template>

<script setup lang="ts">
import { FwbAlert } from 'flowbite-vue'

const showSuccess = ref(false)
const showError = ref(false)
</script>
```

### Modal Dialog

```vue
<template>
  <FwbModal v-if="showModal" @close="showModal = false">
    <template #header>
      <div class="flex items-center text-lg">
        Confirm Delete
      </div>
    </template>
    
    <template #body>
      <p class="text-base leading-relaxed text-gray-500 dark:text-gray-400">
        Are you sure you want to delete this browser profile? This action cannot be undone.
      </p>
    </template>
    
    <template #footer>
      <div class="flex justify-end space-x-4">
        <FwbButton color="alternative" @click="showModal = false">
          Cancel
        </FwbButton>
        <FwbButton color="red" @click="confirmDelete">
          Delete
        </FwbButton>
      </div>
    </template>
  </FwbModal>
</template>

<script setup lang="ts">
import { FwbModal, FwbButton } from 'flowbite-vue'

const showModal = ref(false)

const confirmDelete = () => {
  // Handle deletion
  showModal.value = false
}
</script>
```

## Theme and Dark Mode

### Theme Toggle Component

```vue
<template>
  <FwbButton
    color="alternative"
    size="sm"
    @click="toggleTheme"
    class="p-2"
  >
    <SunIcon v-if="isDark" class="w-5 h-5" />
    <MoonIcon v-else class="w-5 h-5" />
  </FwbButton>
</template>

<script setup lang="ts">
import { FwbButton } from 'flowbite-vue'
import { useDark, useToggle } from '@vueuse/core'

const isDark = useDark()
const toggleTheme = useToggle(isDark)
</script>
```

### Theme Configuration

```typescript
// src/composables/useTheme.ts
import { ref, watch } from 'vue'

export function useTheme() {
  const isDark = ref(false)

  const toggleTheme = () => {
    isDark.value = !isDark.value
    updateTheme()
  }

  const updateTheme = () => {
    if (isDark.value) {
      document.documentElement.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      document.documentElement.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }
  }

  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
    updateTheme()
  }

  return {
    isDark,
    toggleTheme,
    initTheme
  }
}
```

This guide provides comprehensive examples for migrating from Vuetify to Flowbite Vue components. The components offer similar functionality with modern design and excellent dark mode support.
