# Electron Integration Guide for Ghost Automator Pro

This guide provides comprehensive instructions for integrating Electron with the Vue 3 + Flowbite application to create a native Windows desktop application, based on the proven architecture from the research example.

## Table of Contents

- [Overview](#overview)
- [Installation and Setup](#installation-and-setup)
- [Project Structure](#project-structure)
- [Configuration Management](#configuration-management)
- [Main Process Configuration](#main-process-configuration)
- [Preload Script Setup](#preload-script-setup)
- [IPC Communication](#ipc-communication)
- [Browser Automation Integration](#browser-automation-integration)
- [Building and Packaging](#building-and-packaging)
- [Development Workflow](#development-workflow)

## Overview

Ghost Automator Pro uses Electron to package the Vue 3 + Flowbite web application as a native Windows desktop application. This approach provides:

- **Native Desktop Experience**: Window management, system tray, native menus
- **System API Access**: File system operations, system notifications, process management
- **Browser Profile Management**: Direct integration with Chromium browser instances
- **Security**: Sandboxed renderer process with controlled access to system APIs

## Installation and Setup

### 1. Install Electron Dependencies

Based on the research example, install the following dependencies:

```bash
# Core Electron dependencies
npm install --save-dev electron electron-builder electron-vite

# Environment and configuration
npm install env-var

# Browser automation (optional, for advanced features)
npm install playwright playwright-with-fingerprints

# Additional development utilities
npm install --save-dev concurrently wait-on
```

### 2. Update package.json Scripts

Add the following scripts to your `package.json` (based on the research example):

```json
{
  "main": "./build-electron/electron-main/index.js",
  "scripts": {
    "electron:dev": "electron-vite dev",
    "electron:build": "electron-vite build && electron-builder",
    "electron:preview": "electron-vite preview",
    "electron:pack": "electron-vite build && electron-builder --dir"
  },
  "build": {
    "appId": "com.ghostautomator.pro",
    "productName": "Ghost Automator Pro",
    "copyright": "Copyright © 2025 Ghost Automator Pro",
    "electronVersion": "36.3.2",
    "directories": {
      "output": "dist-electron/${version}",
      "buildResources": "build-resources"
    },
    "files": [
      "build-electron/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "extraMetadata": {
      "main": "./build-electron/electron-main/index.js"
    },
    "compression": "maximum",
    "removePackageScripts": true,
    "nodeGypRebuild": false,
    "buildDependenciesFromSource": false,
    "npmRebuild": false,
    "win": {
      "target": [
        {
          "target": "dir",
          "arch": ["x64"]
        }
      ],
      "icon": "build-resources/icon.ico",
      "artifactName": "${productName}-${version}-${arch}",
      "requestedExecutionLevel": "asInvoker",
      "verifyUpdateCodeSignature": false
    }
  }
}
```

### 3. Create Electron Vite Configuration

Create `electron.vite.config.ts` (based on the research example):

```typescript
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import Layouts from 'vite-plugin-vue-layouts-next'
import VueRouter from 'unplugin-vue-router/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import { resolve } from 'path'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      outDir: 'build-electron/electron-main',
      rollupOptions: {
        input: resolve(__dirname, 'src/electron-main/index.ts')
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      outDir: 'build-electron/electron-preload',
      rollupOptions: {
        input: resolve(__dirname, 'src/electron-preload/index.ts')
      }
    }
  },
  renderer: {
    root: '.',
    build: {
      outDir: 'build-electron/renderer',
      rollupOptions: {
        input: resolve(__dirname, 'index.html')
      }
    },
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      VueRouter({
        dts: 'src/typed-router.d.ts'
      }),
      Layouts(),
      AutoImport({
        imports: [
          'vue',
          VueRouterAutoImports,
          {
            pinia: ['defineStore', 'storeToRefs']
          }
        ],
        dts: 'src/auto-imports.d.ts',
        eslintrc: {
          enabled: true
        },
        vueTemplate: true
      }),
      Components({
        dts: 'src/components.d.ts'
      }),
      vue()
    ],
    define: { 'process.env': {} },
    server: {
      port: 3000
    }
  }
})
```

## Project Structure

Based on the research example, the recommended project structure is:

```
ghost-automator-pro/
├── src/                          # Application source code
│   ├── components/               # Vue components
│   ├── views/                    # Vue views/pages
│   ├── layouts/                  # Vue layouts
│   ├── pages/                    # Auto-routed pages
│   ├── stores/                   # Pinia stores
│   ├── config/                   # Application configuration
│   ├── electron-main/            # Electron main process
│   │   ├── index.ts              # Main process entry point
│   │   ├── handlers/             # IPC handlers
│   │   └── services/             # Main process services
│   ├── electron-preload/         # Electron preload scripts
│   │   ├── index.ts              # Preload script
│   │   └── types.d.ts            # Type definitions
│   └── main.ts                   # Vue app entry point
├── build-electron/               # Built Electron files
│   ├── electron-main/            # Built main process
│   ├── electron-preload/         # Built preload scripts
│   └── renderer/                 # Built renderer (Vue app)
├── build-resources/              # Build assets (icons, etc.)
├── public/                       # Static assets
├── electron.vite.config.ts       # Electron Vite configuration
└── package.json                  # Project configuration
```

## Configuration Management

Create a robust configuration system using the research example approach:

### 1. Create AppConfig Class

Create `src/config/AppConfig.ts`:

```typescript
import { config } from 'dotenv'
import env from 'env-var'
import path from 'path'
import fs from 'fs'

export class AppConfig {
  constructor() {
    this.loadEnvironmentFiles()
  }

  private loadEnvironmentFiles(): void {
    const envType = process.env.NODE_ENV || 'development'
    console.log(`🔧 Loading configuration for environment: ${envType}`)

    this.loadEnvFile(`.env.${envType}`)
    console.log(`✅ Environment files loaded for: ${envType}`)
  }

  private loadEnvFile(filename: string): void {
    const filePath = path.resolve(process.cwd(), filename)

    if (fs.existsSync(filePath)) {
      const result = config({ path: filePath })
      console.log(`📄 Loaded: ${filename}`)

      if (result.error) {
        console.warn(`⚠️  Warning loading ${filename}:`, result.error.message)
      }
    } else {
      console.log(`📄 Skipped: ${filename} (not found)`)
    }
  }

  // Application Window Configuration
  public readonly window = {
    width: env.get('VITE_WINDOW_WIDTH').default('1200').asIntPositive(),
    height: env.get('VITE_WINDOW_HEIGHT').default('800').asIntPositive()
  }

  // Application Information
  public readonly app = {
    name: env.get('VITE_APP_NAME').default('Ghost Automator Pro').asString(),
    version: env.get('VITE_APP_VERSION').default('1.0.0').asString()
  }

  // Development Configuration
  public readonly development = {
    openDevTools: env.get('VITE_OPEN_DEVTOOLS').default('true').asBool()
  }

  // Browser Profile Configuration
  public readonly profiles = {
    dataFolder: env.get('PROFILES_DATA_FOLDER').default('.data_profiles').asString()
  }
}

export const appConfig = new AppConfig()
export default appConfig
```

## Main Process Configuration

### 1. Create Electron Main Process

Create `electron/main.ts`:

```typescript
import { app, BrowserWindow, Menu, ipcMain } from 'electron'
import { join } from 'path'
import { isDev } from './utils/env'

let mainWindow: BrowserWindow | null = null

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'preload.js')
    },
    icon: join(__dirname, '../public/icon.ico'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../dist/index.html'))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// App event handlers
app.whenReady().then(() => {
  createWindow()
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// IPC handlers
ipcMain.handle('app-version', () => {
  return app.getVersion()
})

ipcMain.handle('platform', () => {
  return process.platform
})
```

### 2. Create Environment Utilities

Create `electron/utils/env.ts`:

```typescript
export const isDev = process.env.NODE_ENV === 'development'
export const isProduction = process.env.NODE_ENV === 'production'
```

### 3. Create Preload Script

Create `electron/preload.ts`:

```typescript
import { contextBridge, ipcRenderer } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  getVersion: () => ipcRenderer.invoke('app-version'),
  getPlatform: () => ipcRenderer.invoke('platform'),
  
  // Browser profile management
  createProfile: (profileData: any) => ipcRenderer.invoke('create-profile', profileData),
  deleteProfile: (profileId: string) => ipcRenderer.invoke('delete-profile', profileId),
  launchBrowser: (profileId: string) => ipcRenderer.invoke('launch-browser', profileId),
  
  // File operations
  exportProfiles: (profiles: any[]) => ipcRenderer.invoke('export-profiles', profiles),
  importProfiles: () => ipcRenderer.invoke('import-profiles'),
  
  // System notifications
  showNotification: (title: string, body: string) => ipcRenderer.invoke('show-notification', title, body)
})

// Type definitions for TypeScript
declare global {
  interface Window {
    electronAPI: {
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
      createProfile: (profileData: any) => Promise<any>
      deleteProfile: (profileId: string) => Promise<boolean>
      launchBrowser: (profileId: string) => Promise<boolean>
      exportProfiles: (profiles: any[]) => Promise<string>
      importProfiles: () => Promise<any[]>
      showNotification: (title: string, body: string) => Promise<void>
    }
  }
}
```

## Renderer Process Integration

### 1. Update Vue Application for Electron

The Vue application doesn't need major changes, but you can add Electron-specific features:

```typescript
// src/composables/useElectron.ts
import { ref, onMounted } from 'vue'

export function useElectron() {
  const isElectron = ref(false)
  const platform = ref('')
  const version = ref('')

  onMounted(async () => {
    if (window.electronAPI) {
      isElectron.value = true
      platform.value = await window.electronAPI.getPlatform()
      version.value = await window.electronAPI.getVersion()
    }
  })

  return {
    isElectron,
    platform,
    version
  }
}
```

### 2. Browser Profile Management Service

```typescript
// src/services/electronProfileService.ts
export class ElectronProfileService {
  async createProfile(profileData: any) {
    if (window.electronAPI) {
      return await window.electronAPI.createProfile(profileData)
    }
    throw new Error('Electron API not available')
  }

  async deleteProfile(profileId: string) {
    if (window.electronAPI) {
      return await window.electronAPI.deleteProfile(profileId)
    }
    throw new Error('Electron API not available')
  }

  async launchBrowser(profileId: string) {
    if (window.electronAPI) {
      return await window.electronAPI.launchBrowser(profileId)
    }
    throw new Error('Electron API not available')
  }

  async exportProfiles(profiles: any[]) {
    if (window.electronAPI) {
      return await window.electronAPI.exportProfiles(profiles)
    }
    throw new Error('Electron API not available')
  }

  async importProfiles() {
    if (window.electronAPI) {
      return await window.electronAPI.importProfiles()
    }
    throw new Error('Electron API not available')
  }
}
```

## IPC Communication

### 1. Main Process Handlers

Add these handlers to your main process:

```typescript
// electron/main.ts - Add these IPC handlers

import { dialog, shell, Notification } from 'electron'
import { promises as fs } from 'fs'
import { join } from 'path'

// Browser profile management
ipcMain.handle('create-profile', async (event, profileData) => {
  try {
    // Implement profile creation logic
    const profilesDir = join(app.getPath('userData'), 'profiles')
    await fs.mkdir(profilesDir, { recursive: true })
    
    const profilePath = join(profilesDir, `${profileData.id}.json`)
    await fs.writeFile(profilePath, JSON.stringify(profileData, null, 2))
    
    return { success: true, profileId: profileData.id }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

ipcMain.handle('delete-profile', async (event, profileId) => {
  try {
    const profilesDir = join(app.getPath('userData'), 'profiles')
    const profilePath = join(profilesDir, `${profileId}.json`)
    await fs.unlink(profilePath)
    return true
  } catch (error) {
    return false
  }
})

ipcMain.handle('launch-browser', async (event, profileId) => {
  try {
    // Implement browser launching logic with profile
    // This would integrate with your browser automation system
    return true
  } catch (error) {
    return false
  }
})

// File operations
ipcMain.handle('export-profiles', async (event, profiles) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow!, {
      defaultPath: 'ghost-automator-profiles.json',
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      await fs.writeFile(result.filePath, JSON.stringify(profiles, null, 2))
      return result.filePath
    }
    return null
  } catch (error) {
    throw error
  }
})

ipcMain.handle('import-profiles', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow!, {
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const data = await fs.readFile(result.filePaths[0], 'utf-8')
      return JSON.parse(data)
    }
    return []
  } catch (error) {
    throw error
  }
})

// System notifications
ipcMain.handle('show-notification', async (event, title, body) => {
  new Notification({ title, body }).show()
})
```

## Building and Packaging

### 1. Build Configuration

Create `electron-builder.config.js`:

```javascript
module.exports = {
  appId: 'com.ghostautomator.pro',
  productName: 'Ghost Automator Pro',
  directories: {
    output: 'dist-electron-pack'
  },
  files: [
    'dist/**/*',
    'dist-electron/**/*',
    'node_modules/**/*'
  ],
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ],
    icon: 'public/icon.ico',
    requestedExecutionLevel: 'asInvoker'
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'Ghost Automator Pro'
  }
}
```

### 2. TypeScript Configuration

Update `tsconfig.json` to include Electron files:

```json
{
  "compilerOptions": {
    // ... existing options
  },
  "include": [
    "src/**/*",
    "electron/**/*"
  ]
}
```

## Development Workflow

### 1. Development Commands

```bash
# Start Vue dev server only
npm run dev

# Start Electron in development mode
npm run electron:dev

# Build Vue application
npm run build

# Package Electron app (without installer)
npm run electron:pack

# Build and create installer
npm run electron:build
```

### 2. Debugging

- **Renderer Process**: Use browser dev tools (automatically opened in dev mode)
- **Main Process**: Use VS Code debugger or add `--inspect` flag to Electron
- **IPC Communication**: Add logging to both main and renderer processes

### 3. Hot Reload

The development setup supports hot reload for the Vue application. Electron will automatically reload when the renderer process changes.

This guide provides the foundation for integrating Electron with your Vue 3 + Flowbite application. The next steps would be to implement the specific browser profile management features and integrate with your fingerprinting and automation systems.
