# UI Design Requirements for `Ghost Automator Pro`

## 📋 Overview

`Ghost Automator Pro` requires a modern, intuitive, and professional user interface that reflects its purpose as a
powerful Browser Profile management tool. For development guidelines, please refer to
the [DEVELOPMENT.md](../../DEVELOPMENT.md) file.

## 📖 Key Definitions

### Browser Profile vs. User Profile

**Browser Profile**: A complete configuration package that defines how a browser instance behaves, including:

- Browser fingerprint (user agent, screen resolution, installed fonts, etc.)
- Proxy settings and network configuration
- Browser extensions and their configurations
- Cookies, local storage, and browsing history
- Security and privacy settings

**User Profile** (Future Feature): Application-level user accounts that will contain:

- User preferences and application settings
- Collections of Browser Profiles owned by the user
- User authentication and access permissions
- Personal workspace and project organization

*Note: This application currently focuses on Browser Profile management. User Profile functionality will be added in
future versions to support multi-user environments and advanced profile organization.*

### Core User Workflow

The core workflow in `Ghost Automator Pro` consists of three main steps:

1. **Use Browser Profiles**: Users set up and use browser profiles, tasks, and other system components to perform
   automated browsing activities.
2. **System Prepares Browser Profiles**: The system automatically prepares browser profiles by running tasks that start
   browser instances, perform web searches, and emulate human browsing behavior.
3. **Export Data**: Users can export all necessary data to work with browser profiles in the future, including complete
   archives or individual components like fingerprints and cookies.

This workflow enables users to create, maintain, and reuse high-quality browser profiles across different sessions and
environments.

### Browser and Fingerprint Technology

`Ghost Automator Pro` uses the following browser technology approach:

- **Base Browser Engine**: The application uses **only Chromium** as the actual browser engine for all browser profiles.
- **Fingerprint Emulation**: While using Chromium as the base, the system can create fingerprints that mimic
  various Chromium-based browsers:
    - For desktop: Chrome, Edge, Opera, Yandex, and other Chromium-based browsers
    - For mobile: Chrome, Samsung Browser, and other Chromium-based mobile browsers
- **Distinction**: It's important to understand that the actual browser executable is always Chromium, but the
  fingerprint (how the browser appears to websites) can be configured to mimic different Chromium-based browsers.

## 📱 Required Pages

The application will include the following pages:

1.**Dashboard** Main overview page displaying key metrics, charts, and recent activity

- Shows statistics for Active Browser Profiles, Running Tasks, Completed Tasks, and Failed Tasks
- Displays performance charts and browser profile distribution
- Lists recent activities with status and timestamps

2.**Browser Profiles** Comprehensive management of browser profiles and all related components

- Lists all created browser profiles with search and filter functionality
- Provides options to view, edit, and delete existing browser profiles
- Includes browser profile status indicators and quick action buttons
- Displays integrated information about fingerprints, proxies (including security, WebRTC, DNS, and IP settings), and
  extensions for each profile
- Offers unified management of all profile-related settings in one place
- Provides visual indicators for proxy configuration status and security level

3.**Create/Edit Browser Profile** Integrated form for creating and editing browser profiles

- Includes fields for browser profile name, browser version, and configuration options
- Provides fingerprint selection and customization as part of the profile setup
- Contains comprehensive proxy configuration options within the same workflow, including security, WebRTC, DNS, and IP
  detection settings
- Includes extension management directly in the profile creation/editing process
- Offers target website optimization settings for fingerprinting
- Provides a streamlined, step-by-step workflow for complete profile setup

4.**Import/Export Browser Profiles** Comprehensive tools for importing and exporting complete browser profiles

- **Export Options**:
    - **Complete Archive Export**: Export all or selected browser profiles as a complete archive file (.zip)
    - **Component-Level Export**: Export specific components of browser profiles:
        - Fingerprint files (JSON format)
        - Cookies (JSON, Chrome DevTools Protocol format
          only: https://chromedevtools.github.io/devtools-protocol/tot/Storage/#method-getCookies)
    - **Format Selection**: Choose between different export formats based on the component

- **Import Options**:
    - **Archive Import**: Import complete browser profile archives
    - **Validation**: Automatic validation of imported files for integrity and compatibility

- **Batch Operations**:
    - Supports bulk export/import of multiple browser profiles simultaneously
    - Batch processing with progress indicators for large operations

- **Export/Import History**:
    - Maintains a log of recent export/import operations
    - Quick access to recently exported files

- **Data Integrity**:
    - Ensures all profile components (cookies, proxies, fingerprints, extensions) are included in exports
    - Validates the integrity and completeness of imported profiles
    - Provides detailed reporting for failed and successful imports

5.**Active Tasks** Overview of currently running tasks and automation workflows

- Displays real-time status and progress of tasks
- Provides pause/resume/stop controls
- Includes detailed task information and logs
- Shows performance metrics and resource usage for each task
- Displays real-time screenshots of running browser instances

## 📋 Active Tasks

The Active Tasks page provides a comprehensive view of all running tasks and automation workflows, allowing users to
monitor, control, and manage their automated browser operations in real-time.

In `Ghost Automator Pro`, tasks primarily involve starting browser instances, performing web searches, and emulating
human browsing behavior to prepare browser profiles for future use.

### UI Components

The Active Tasks page is organized into three main sections:

1.**Task Control Panel**

- **Filter Controls**: Allows filtering tasks by:
    - Status (Running, Paused, Completed, Failed)
    - Categories (multi-select dropdown with keywords assigned to tasks)
    - Browser Profile (dropdown of all available profiles)
    - Date Range (when tasks were started)
- **Batch Action Buttons**: Enables operations on multiple selected tasks:
    - Stop Selected: Terminates selected tasks
    - Restart Selected: Stops and restarts selected tasks
- **Create Task Button**: Prominent button to create a new automated task

2.**Active Tasks Table**

- **Data Table**: Displays all tasks with the following columns:
    - **Task Name**: The name of the task
    - **Task Type**: The type of automation being performed
    - **Categories**: The categories assigned to the task (displayed as chips for multiple keywords)
    - **Browser Profile**: The browser profile being used
    - **Target Website**: The website the task is operating on
    - **Progress**: Visual progress bar showing completion percentage
    - **Status**: Current status (Running, Paused, Completed, Failed) with color-coded indicators
    - **Started**: When the task was started (formatted date and time)
    - **Duration**: How long the task has been running
    - **Resource Usage**: CPU and memory consumption
    - **Actions**: Buttons for pause/resume, stop, view details, and delete
- **Expandable Rows**: Each row can be expanded to show:
    - Detailed logs and console output
    - Step-by-step execution status
    - Error messages and warnings
    - Performance metrics

3.**Task Monitoring Grid**

- **Browser Screenshots Grid**: Displays a grid of real-time screenshots from all running browser instances:
    - Each cell represents a running browser task
    - Screenshots are automatically refreshed every 5 seconds
    - Color-coded borders indicate task status (running, paused, error)
    - Hover tooltips show basic task information (name, browser profile, target website)
    - Grid layout is responsive and adjusts based on screen size and number of tasks
- **Interactive Features**:
    - Clicking on a screenshot opens a larger view of the browser state
    - The larger view shows the current mocked website the browser is interacting with
    - In future versions, this will be integrated with Electron or similar tools to display the actual browser window
    - Option to increase/decrease grid density (larger/smaller thumbnails)
- **Grid Controls**:
    - Filter controls to show only specific types of tasks or categories
    - Sort options (by status, duration, progress, categories)

### Functionality

The Active Tasks page provides the following key functionality:

1.**Task Monitoring**

- System used for preparing browser profiles for future use, with wrap-up tasks and shipped with high-quality
  fingerprints
- Monitoring of browser instances that are performing Google searches and emulating human browsing behavior
- Tracking of human-like interactions including search queries, link clicking, page scrolling, and natural browsing
  patterns
- Real-time tracking of all running tasks with status updates
- Visual progress indicators showing completion percentage
- Grid view of browser screenshots for visual monitoring of multiple tasks simultaneously
- Interactive screenshots that can be clicked to show a detailed view of the current mocked website
- Future integration with Electron to display actual browser windows when clicked
- Resource usage monitoring (CPU, memory, network)
- Detailed logs and console output for debugging
- Performance metrics and statistics

2.**Task Control**

- Start, pause, resume, and stop individual or multiple browser tasks
- Control the pace and pattern of web searches and browsing behavior to ensure natural human-like interactions
- Adjust search parameters, browsing depth, and interaction patterns while tasks are running
- Modify the human emulation parameters (scrolling speed, click patterns, typing cadence) in real-time
- Set breakpoints for debugging complex workflows
- Capture screenshots of the current browser state
- Restart failed tasks with optional parameter adjustments

3.**Task Management**

- Filter and sort browser automation tasks based on various criteria
- Search for specific tasks by name, target website, or search query content
- Save and load browser task configurations for different search patterns and browsing behaviors
- Schedule browser profile warm-up tasks for future execution
- Set up recurring task schedules for continuous browser profile maintenance
- Group related tasks for batch management of similar browser profiles

4.**Automation Workflow**

- Create complex multistep browser automation workflows that mimic natural human web browsing
- Define search patterns and browsing sequences that appear human-like
- Configure realistic timing between actions (searches, clicks, scrolling) to avoid detection
- Define conditional logic and branching paths based on search results and page content
- Create randomized variations in browsing behavior to appear more natural
- Set up error handling and recovery procedures for website changes or blocking
- Configure retry logic for failed steps with alternative browsing patterns
- Define success/failure criteria based on completed browsing sessions and collected cookies
- Set up notifications for completed browser profile preparation

The Active Tasks page automatically updates in real-time to show the current status of all tasks, with more detailed
updates available for the selected task. Users can customize the refresh rate and level of detail displayed.

6.**General Settings** Basic application configuration

- Contains minimal application preferences (language and theme)
- Includes basic notification options

## ⚙️ General Settings

The General Settings page provides minimal configuration options for the application, focusing only on the most
essential settings.

### UI Components

The General Settings page is organized into a single simple section:

1.**Basic Preferences**

- **Language**: Simple language selector (English, Spanish, German, Russian)
- **Theme**: Light/Dark mode toggle
- **Notifications**: Enable/disable system notifications

### Functionality

The General Settings page provides the following essential functionality:

- Change application language
- Toggle between light and dark mode
- Enable or disable notifications

All settings are automatically saved as they are changed, with a simple visual confirmation.

## 📊 Dashboard

The dashboard serves as the main landing page and control center for the application, providing users with a
comprehensive overview of the system status, performance metrics, and recent activities.

### UI Components

The dashboard UI is organized into four main sections:

1.**Statistics Cards**

- Four prominently displayed cards at the top of the dashboard
- Each card uses a distinct color scheme for visual differentiation:
    - Active Browser Profiles (Primary color)
    - Running Tasks (Success/Green color)
    - Completed Tasks (Info/Blue color)
    - Failed Tasks (Warning/Orange color)
- Each card displays a headline figure and detailed breakdown statistics

2.**System Resources Monitor**

- Horizontal progress bars showing real-time resource utilization
- Visual indicators for CPU, Memory, Storage, and Browser Profiles Storage usage
- Percentage values displayed within each progress bar
- Color-coded bars for different resource types

3.**Performance Charts**

- Two side-by-side charts providing visual data representation:
    - Task Performance Chart (Line/Bar chart showing success rate, failure rate, and average duration)
    - Browser Profile Distribution Chart (Pie chart showing fingerprint browser type distribution)
- Interactive legends explaining the data representation
- Clear labeling of axes and data points

4.**Recent Activity Table**

- Comprehensive table listing recent system activities
- Columns for Activity Type, Browser Profile Name, Fingerprint Browser Type, Status, Duration, and Timestamp
- Status indicators using color-coded chips (Success, Running, Failed, Pending)
- Action buttons for each activity entry
- "View All" button to access the complete activity history

### Functionality

The dashboard provides the following key functionality:

1.**System Monitoring**

- Real-time tracking of active browser profiles with breakdown by fingerprint browser type
- Monitoring of running tasks with categorization by task type
- Tracking of completed tasks with time-based grouping (today, this week, this month)
- Alerting of failed tasks with error type classification
- Real-time system resource utilization monitoring

2.**Performance Analysis**

- Visual representation of task performance metrics over time
- Success and failure rate tracking with trend analysis
- Task duration monitoring to identify performance issues
- Browser profile usage distribution analysis
- Resource consumption patterns identification

3.**Activity Management**

- Quick access to recent system activities
- Ability to view detailed information about specific activities
- Direct actions on activities (stop running tasks, retry failed tasks)
- Filtering and sorting capabilities for activity data
- Navigation to full activity history

4.**Quick Actions**

- Direct access to frequently used functions
- Ability to stop or restart tasks directly from the dashboard
- Navigation shortcuts to detailed views
- System status alerts and notifications

The dashboard automatically refreshes at regular intervals to ensure data accuracy, with system resource monitoring
updated every few seconds. Users can also manually refresh the data as needed.

## 🖥️ Browser Profiles

The Browser Profiles page serves as the central hub for managing all Browser Profiles in the application. It provides a
comprehensive Excel-like table view with powerful management tools and detailed information about each Browser Profile.

The interface is designed to be familiar to users who work with spreadsheet applications, offering similar functionality
and visual appearance.

### UI Components

The Browser Profiles page is organized into two main sections:

1.**Table Toolbar**

- **Search Field**: Allows users to search across all browser profile properties
- **Filter Button**: Opens a dropdown with multiple filtering options:
    - Target Website (multi-select dropdown with predefined websites that browser profiles are optimized for)
    - Fingerprint Browser Type (dropdown with options for Chrome, Edge, Opera, Yandex, and other Chromium-based
      browsers)
    - Status (dropdown with options for active, inactive, error)
    - Tags (multi-select dropdown with custom tags applied to browser profiles)
- **Column Button**: Allows users to customize which columns are displayed in the table
- **Batch Action Buttons**: Enables operations on multiple selected browser profiles:
    - Delete Selected: Removes all selected browser profiles
    - Export Selected: Exports all selected browser profiles (as complete archives or individual components like
      fingerprints, cookies, etc.)
- **Create Browser Profile Button**: Prominent button to create a new browser profile

2.**Browser Profiles Table**

- **Excel-like Data Table**: Displays all browser profiles in a spreadsheet-like interface with the following features:
    - **Grid Layout**: Clean, Excel-like grid with rows and columns
    - **Cell Selection**: Ability to select individual cells, rows, columns, or ranges
    - **Keyboard Navigation**: Arrow keys for cell navigation, Tab/Shift+Tab for horizontal movement
    - **Column Headers**: Interactive headers with sort indicators and filter dropdowns (clicking a filter icon in a
      column header opens the same filtering options as the Filter Button)
    - **Row Highlighting**: Hover and selection highlighting similar to Excel
    - **Fixed Headers**: Column headers remain visible when scrolling vertically

- **Table Columns**: The table includes the following columns:
    - **Name**: The name of the browser profile
    - **Target Website**: The predefined target website(s) for which the browser profile is optimized (displayed as
      chips
      for multiple websites)
    - **Fingerprint Browser Type**: The type of browser fingerprint (Chrome, Edge, Opera, Yandex, and other
      Chromium-based
      browsers) with the appropriate icon
    - **Version**: The browser version
    - **Proxy**: Whether a proxy is enabled (displayed as a chip: Enabled/Disabled)
    - **Created**: When the browser profile was created (formatted date)
    - **Last Used**: When the browser profile was last used (formatted date)
    - **Status**: The current status of the browser profile (color-coded chip: active, inactive, error)
    - **Tags**: Tags associated with the browser profile (displayed as small chips)
    - **Actions**: Buttons for edit, launch, and delete operations

- **Excel-like Features**:
    - **Column Resizing**: Users can adjust column widths by dragging column dividers
    - **Column Reordering**: Users can change column order via drag and drop
    - **Quick Filters**: Filter icons in column headers for rapid data filtering, providing the same dropdown options as
      the main Filter Button but accessible directly from each column
    - **Context Menus**: Right-click menus for common operations
    - **Keyboard Shortcuts**: Excel-familiar shortcuts for selection, copying, and navigation
    - **Cell Tooltips**:
        - Hover tooltips for cells with truncated content
        - Detailed information tooltips that appear when clicking on a cell to show more comprehensive data about the
          browser profile
        - For Fingerprint Browser Type: clicking reveals detailed fingerprint parameters, browser engine version, and
          compatibility notes
        - For Proxy: clicking shows the complete proxy configuration including authentication details, location, and
          connection
          status
        - For Status: clicking displays detailed status information, error messages if applicable, and troubleshooting
          suggestions
        - For Tags: clicking shows full tag descriptions and related browser profiles with the same tags

- **Pagination Controls**: Controls at the bottom of the table:
    - Page selector
    - Items per page dropdown (5, 10, 15, 20, 50)
    - Page information display (e.g., "Showing 1–10 of 24 items")

### Functionality

The Browser Profiles page provides the following key functionality:

1.**Browser Profile Management**

- Create, edit, and delete browser profiles
- Launch browser sessions with selected browser profiles
- View detailed information about each browser profile
- Batch operations for multiple browser profiles (delete, export as archives or individual components)

2.**Advanced Filtering and Search**

- Full-text search across all browser profile properties
- Filter by target website, fingerprint browser type, status, and tags:
    - **Target Website**: Filter using a multi-select dropdown from a predefined list of websites. The list is
      maintained
      by administrators and includes commonly used websites for automation.
    - **Fingerprint Browser Type**: Filter using a dropdown with options for Chrome, Edge, Opera, Yandex, and other
      Chromium-based browsers.
    - **Status**: Filter using a dropdown with options for active, inactive, and error states.
    - **Tags**: Filter using a multi-select dropdown with all custom tags that have been applied to browser profiles.
- Combine multiple filters for precise results (e.g., show all active Chrome profiles for a specific target website)

3.**Excel-like Table Customization**

- Show/hide specific columns based on user preference (similar to Excel's column visibility options)
- Sort by any column (except Tags and Actions) with multi-level sorting capability
- Adjust the number of items displayed per page
- Select multiple browser profiles for batch operations using Excel-like selection patterns
- Freeze columns to keep important information visible while scrolling horizontally
- Save custom table views and layouts for quick access
- Export table data to actual Excel/CSV formats for external analysis
- Apply conditional formatting rules to highlight cells based on their values

4.**Visual Status Indicators**

- Color-coded status chips (green for active, grey for inactive, red for error)
- Fingerprint browser icons for quick identification
- Tag chips for visual categorization
- Last activity timestamp for usage tracking

The Browser Profiles table automatically updates when changes are made to browser profiles, and users can manually
refresh the data as needed.

The table supports keyboard navigation and provides tooltips for actions and status indicators, as well as detailed
information tooltips for cells that can be accessed by clicking on them to reveal more comprehensive data about browser
profiles.

## 🏗️ Layout Structure

All pages follow a consistent layout with:

- **Header**: Contains logo, search bar, language selector, and notifications
- **Left Sidebar**: Contains the main navigation menu
- **Main Content**: Primary content area that changes based on the selected page
- **Right Panel (Optional)**: Contains contextual information and quick actions where appropriate

The application uses a component-based architecture with reusable elements across all pages to ensure consistency and
maintainability.

## 🔔 Error Handling and Notifications

`Ghost Automator Pro` implements a comprehensive error handling and notification system to ensure users are informed of
important events and can effectively troubleshoot issues.

### Error Handling

The application employs a multi-layered approach to error handling:

1.**Error Prevention**

- **Input Validation**: All user inputs are validated in real-time with clear visual indicators
- **Confirmation Dialogs**: Potentially destructive actions require confirmation
- **Predictive Warnings**: The system identifies and warns about potential issues before they occur
- **Guided Corrections**: When errors are detected, the system provides specific guidance on how to fix them

2.**Error Classification**

- **Critical Errors**: System-level issues that prevent core functionality (red indicators)
- **Functional Errors**: Issues that impact specific features but allow continued use (orange indicators)
- **Warning States**: Potential issues that may require attention (yellow indicators)
- **Information Notices**: Non-critical information about system behavior (blue indicators)

3.**Error Presentation**

- **Contextual Error Messages**: Errors are displayed directly next to the relevant UI element
- **Error Summaries**: Complex forms show a summary of all validation issues
- **Detailed Error Pages**: System-level errors include detailed information and troubleshooting steps
- **Error Codes**: Unique error codes for easy reference and support communication

### Notification System

A minimal notification system for essential alerts:

1.**Basic Alerts**

- **Task Status**: Alerts for completed or failed tasks
- **System Status**: Critical system information

2.**Display**

- **Header Indicator**: Simple notification icon in header

The system provides only essential information with minimal interruption.

The error handling and minimal notification approach work together to provide a streamlined user experience that focuses
on essential information only.

## 👤 User Workflow

### Browser Profile Creation and Management

The typical user workflow for creating and managing browser profiles follows these steps:

1.**Create New Browser Profile**

- User navigates to the Browser Profiles page and clicks "Create New Browser Profile"
- User enters a name for the browser profile (a unique name is automatically generated by default, which the user can
  modify)
- Selects one or more target websites from a predefined list (important for fingerprint optimization)
- User selects the Chromium browser executable version to be used (Chromium 136, 137, etc.)

2.**Configure Fingerprint Request Parameters**

- User chooses:
    - Device type (mobile or desktop)
    - Operating system (Windows 10/11 for desktop, Android for mobile)
- User configures fingerprint parameters for the selected target websites
- Based on the target websites selection, the system suggests optimized fingerprint settings
- Users can customize fingerprint parameters including:
    - Fingerprint browser type and version (can be set to mimic Chrome, Edge, Opera, Yandex, and other Chromium-based
      browsers, regardless of the actual Chromium version installed)
    - Screen resolution

- The system automatically adjusts fingerprint request settings based on the selected Chromium browser executable
  version:
    - For example, if Chrome 136 is selected, the fingerprint request will include this version information
    - For some browser types, the relationship is more complex (e.g., Chrome 136 might correspond to Yandex browser
      27.6.x)
    - These version mapping rules are initially mocked in the system but will be loaded from an API in the future

- Each parameter is tailored to create a consistent and realistic fingerprint optimized for the selected target websites
- These parameters will be used to request a fingerprint from the service
- User reviews all fingerprint parameters
- User requests a fingerprint from the service with the configured parameters
- The system receives the fingerprint from the service and saves it

3.**Apply Fingerprint Settings**

- The system applies the saved fingerprint to the browser profile by configuring browser settings:
    - Use PerfectCanvas(`true`/`false`, default: `true`). If this setting is set to true, PerfectCanvas replacement will
      be enabled. Fingerprint must contain PerfectCanvas data to make it work.
    - Add noise to canvas data(`true`/`false`, default: `true`). If this setting is set to true, canvas will be enabled
      and noise will be added to all data returned from canvas.
    - Add noise to WebGL data(`true`/`false`, default: `false`). If this setting is set to true, WebGL will be enabled,
      noise will be added to WebGL canvas, and your hardware properties, like video card vendor and renderer, will be
      changed.
    - Add noise to audio data(`true`/`false`, default: `false`). If this setting is set to true, audio will be enabled,
      noise will be added to sound, and your hardware properties, like sample rate and number of channels, will be
      changed.
    - Safe Battery(`true`/`false`, default: `true`). If this setting is set to true, battery API will show different
      values for each thread; this prevents sites from detecting your real identity. In case if a device from which
      a fingerprint was obtained doesn't have battery API, 100% charge level will always be returned.
    - Use font pack(`true`/`false`, default: `true`). By default, the browser searches for fonts only in the system font
      folder. This may lead to inconsistencies during fingerprint emulation if the target fingerprint has more fonts
      than
      a local system. Therefore, it is recommended to download font pack with the most popular fonts. This setting
      allows
      using a font pack if it is installed.
    - Safe Element Size(`true`/`false`, default: `true`). If this setting is set to true, the results will be updated to
      protect against 'client rects' fingerprinting.
    - Emulate Sensor API (`true`/`false`, default: `true`). If enabled, the browser will emulate sensor data:
      accelerometer, gyroscope, etc. that is normally only available on mobile devices. This helps create more realistic
      mobile fingerprints by automatically generating and replacing sensor data.
    - **Emulate device scale factor** (`true`/`false`, default: `true`): Enables high-fidelity emulation of devices with
      higher pixel density. When enabled:
        - Browser renders pages at higher resolution matching real devices
        - Provides more natural and accurate device emulation
        - Note: May increase system resource usage due to higher resolution rendering
        - JavaScript properties like devicePixelRatio will be correctly emulated regardless of this setting

4.**Set Up Proxy Configuration**

- User selects a proxy type (HTTP, SOCKS5, etc.)
- User enters proxy details (IP address, port, authentication if required)
- System validates proxy connection and displays status
- User configures additional `proxy settings` organized in groups:
  1.**Security settings**: The options below will help you to adjust different browser settings to match the new
  proxy, for example, timezone and geolocation. Default settings will work fine.
    - **Change timezone** (`true`/`false`, default: `true`). Change browser timezone according to proxy ip. For example,
      if proxy is located in the United Kingdom, then browser timezone offset will be equal to 0(UTC+00:00):    
      -`true`: Change timezone.
      -`false`: Don't change timezone.
    - **Change geolocation** (`true`/`false`, default: `true`).Change browser geolocation according to proxy ip.
      Location will be set to a point which is close to proxy longitude/latitude:
      -`true`: A browser request to access your geolocation will be accepted. Browser location will be set to a
      proxy location.
      -`false`: Don't change geolocation. A browser request to access your geolocation will be rejected.
    - **Change browser language**  (`true`/`false`, default: `true`).Change browser language according to country of
      proxy. This setting will change 'Accept-Language' header as well as `navigator.language` and `navigator.languages`
      javascript properties. By default, language value will be equal to language and country code separated with dash,
      for example `de-DE` for Germany. This value is valid, but most browsers use more complicated strings. If you want
      to make the browser look like a real browser, use FingerprintSwitcher service; it will set language to more
      natural value,
      for example, for iPhone from Germany it will equal `de, en;q=0.8, *;q=0.01`:
      -`true`: Change browser language to match proxy country.
      -`false`: Don't change browser language.

  2.**WebRTC settings**. The options below allow you to set IPs exposed through WebRTC. Default settings will work
  fine.
    - **WebRTC mode**(`enable`/`disable`/`replace`, default: `replace`).This option defines how WebRTC replacement will
      work.
        - `enable`: Disable WebRTC API completely.
        - `disable`:  Enable WebRTC with all requests routed through the proxy. Ensure the proxy supports UDP for
          requests
          to complete successfully.
        - `replace`:  Enable WebRTC, replace exposed IPs with proxy IP or custom values (configurable in advanced
          settings).

  **Note**: Additional WebRTC and proxy configuration settings will be added in future versions.

5.**Manage Browser Extensions**

- User selects extensions to include in the browser profile
- User configures extension settings as needed

6.**Save and Test Browser Profile**

- User saves the complete browser profile
- System validates all settings for consistency
- User can test the browser profile by launching the Chromium browser with the configured fingerprint and proxy settings

This integrated workflow ensures that all aspects of browser profile management are handled in a cohesive process, with
each component (fingerprint, proxy, extensions) working together to create a consistent and effective browsing
environment optimized for the selected target websites.

### Automated Task Creation and Management

The system tasks in `Ghost Automator Pro` focus on the essential functionality needed for effective browser profile
management. The simplified workflow for creating and managing these automated browser tasks follows these steps:

1.**Basic Task Creation**

- User navigates to the Active Tasks page and clicks "Create New Task"
- User enters a task name
- User selects a browser profile to use for the task
- User selects a basic task type (e.g., Google Search)

2.**Essential Task Parameters**

- User configures a minimal set of parameters:
    - Keywords/search terms (simple text input, one per line)
    - Run duration (simple slider or numeric input)
    - Start time (immediate or scheduled)
- All other parameters use system defaults

3.**Task Execution Controls**

- Basic control buttons:
    - Start task
    - Stop task
    - Restart task
- Simple status indicator showing if a task is running, completed, or failed

4.**Basic Task Monitoring**

- Simple list view of all tasks with essential information:
    - Task name
    - Status (Running/Completed/Failed)
    - Start time
    - Duration
    - Browser profile used
- Basic real-time updates of task status
- Simple log view showing key events during task execution

This streamlined approach focuses on delivering the essential task functionality without complex configuration options
or advanced monitoring features. Users can create tasks, set basic parameters, run them, and monitor their
status—providing the core workflow needed for effective browser profile management.

### Multi-Browser Preparation Workflow

`Ghost Automator Pro` supports a specialized workflow for preparing browser profiles through automated Google searching
and web browsing. This workflow is particularly useful for warming up browser profiles before using them for main tasks:

1.**Start Multiple Browser Instances**

- User selects multiple browser profiles to launch simultaneously
- System manages concurrent browser instances based on system resources and configured limits
- Each browser instance is initialized with its assigned profile's fingerprint and proxy settings
- User can specify the number of concurrent browser instances to run

2.**Perform Google Search Preparation**

- For each browser instance, the system performs the following actions:
    - Navigate to Google search page
    - Enter predefined or randomly selected keywords from a customizable list
    - Execute the search and wait for results to load
    - Analyze search results and select links based on configurable criteria
    - Click on selected links and browse the resulting websites
    - Perform natural browsing behaviors (scrolling, clicking, short pauses)
    - Return to Google and perform additional searches with different keywords
    - Repeat the process for a configurable number of iterations

3.**Configure Preparation Parameters**

- Users can customize various aspects of the preparation workflow:
    - Search keywords configuration:
        - Manual entry of specific keywords or phrases to search in the text area
        - Import keyword lists from a text file
    - Search behavior settings:
        - Number of searches per browser instance (total count or time-based)
        - Time between consecutive searches (randomized intervals)
    - Website interaction configuration:
        - Time spent on each website (fixed duration or randomized ranges with min/max values)
        - Browsing depth (how many links deep to follow from each search result)
        - Return-to-search behavior (when to go back to search results)

4.**Monitor Preparation Progress**

- Users can view the real-time status of all browser instances in a unified dashboard
- System provides metrics on searches performed, websites visited, and time elapsed
- Visual indicators show which stage each browser instance is currently executing
- Detailed logs capture all actions for later review

This specialized preparation workflow serves as a core component of the browser profile preparation process, creating
authentic browsing histories, cookies, and behavioral patterns through realistic human-like interactions. By
systematically emulating natural human web searching and browsing behavior, the system ensures that browser profiles
develop the necessary characteristics to appear genuine and avoid detection. This preparation is essential for creating
high-quality browser profiles that can be used for their intended purposes with minimal risk of being identified as
automated.

## 🔄 Demo Data Management

The application requires a structured approach to demo data that ensures flexibility, maintainability, and future
extensibility.

### Demo Data Architecture

Demo data in `Ghost Automator Pro` follows these key principles:

1.**Separation of Concerns**

- Demo data must be created and maintained as a separate, independent component
- Demo data must NOT be hardcoded directly in UI templates or components
- Demo data MUST NOT be included directly in HTML pages
- A dedicated service or component should be created to return demo data
- In future versions, this data will be fetched from a server or read from the filesystem
- All demo data should be structured in a format that can be easily replaced with real data sources

2.**Data Structure Requirements**

- Demo data should mirror the exact structure and format of production data
- All demo data should include realistic values that represent common use cases
- Edge cases and error states should be represented in the demo data

3.**Retrieval Mechanism**

- The application should implement a service layer for data retrieval that abstracts the data source
- Demo data should be designed to be fetched from various potential sources:
    - Local disk storage
    - Remote API endpoints
    - Database systems
    - Configuration files
- The retrieval mechanism should be consistent regardless of the actual data source

4.**Transition Strategy**

- The system should allow for seamless transition between demo data and production data
- Feature flags or configuration settings should control whether demo or real data is used
- Switching between data sources should not require code changes

This approach ensures that demo data serves its purpose during development and testing while maintaining a clean
separation that allows for easy replacement with real data sources in the future.
