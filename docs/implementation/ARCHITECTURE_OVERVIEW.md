# Architecture Overview

This document provides a comprehensive overview of the technical architecture, technology stack, and system design of
`Ghost Automator Pro`.

## Table of Contents

- [Technology Stack](#technology-stack)
- [Application Architecture](#application-architecture)
- [Core Components](#core-components)
- [Integration Points](#integration-points)
- [Data Management](#data-management)

## Technology Stack

### Desktop Application Framework

**Electron**
`Ghost Automator Pro` is built with [Electron](https://www.electronjs.org/), which allows us to create native desktop
applications using web technologies. This approach provides:

- Native Windows application experience with modern web UI
- Access to system-level APIs for Browser Profile management
- Seamless integration with Chromium browser instances
- Cross-platform compatibility (Windows focus with potential for other platforms)
- Modern development workflow using Vue 3, Flowbite, and Tailwind CSS

The application combines the power of Electron with advanced fingerprinting techniques to provide a comprehensive
Browser Profile management solution.

### Frontend Technologies

**Vue 3**

- Modern reactive frontend framework
- Composition API for better code organization
- TypeScript support for type safety
- Component-based architecture for maintainability

**Flowbite + Tailwind CSS**

- Professional UI component library
- Utility-first CSS framework
- Responsive design capabilities
- Dark/light theme support
- Consistent design system

### Browser Automation

**Playwright with Fingerprints**
`Ghost Automator Pro`
utilizes [playwright-with-fingerprints](https://github.com/CheshireCaat/playwright-with-fingerprints) for enhanced
stealth automation capabilities:

- **Enhanced Privacy**: Modified browser fingerprints for stealth automation and detection avoidance
- **Anti-Detection**: Advanced fingerprint modifications to bypass modern anti-bot systems
- **Service Integration**: Support for both free and premium fingerprint services
- **Windows Optimized**: Specifically designed and tested for Windows environments
- **Chromium Engine**: Uses Chromium with sophisticated fingerprint modifications

This integration provides professional-grade browser automation with advanced privacy features, making it ideal for
creating and managing Browser Profiles that can successfully interact with modern websites and anti-bot systems.

### Development Tools

- **TypeScript**: Type safety and better development experience
- **Vite**: Fast build tool and development server
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Vitest**: Unit testing framework

## Application Architecture

### Electron Architecture

```
┌───────────────────────────────────────────────────────────┐
│                  Electron Application                     │
├───────────────────────────────────────────────────────────┤
│ Main Process (Node.js)                                    │
│ ├─ Application lifecycle management                       │
│ ├─ Window management                                      │
│ ├─ System integration                                     │
│ ├─ File system operations                                 │
│ └─ Browser automation orchestration                       │
├───────────────────────────────────────────────────────────┤
│ Renderer Process (Chromium)                               │
│ ├─ Vue 3 application                                      │
│ ├─ Flowbite UI components                                 │
│ ├─ User interface logic                                   │
│ └─ IPC communication with main process                    │
└───────────────────────────────────────────────────────────┘
```

### Frontend Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Vue 3 Application                        │
├─────────────────────────────────────────────────────────────┤
│  Router (Vue Router)                                        │
│  ├─ Dashboard                                               │
│  ├─ Browser Profiles                                        │
│  ├─ Active Tasks                                            │
│  ├─ Import/Export                                           │
│  └─ Settings                                                │
├─────────────────────────────────────────────────────────────┤
│  State Management (Pinia)                                   │
│  ├─ Browser Profile store                                   │
│  ├─ Task management store                                   │
│  ├─ Application settings store                              │
│  └─ User interface store                                    │
├─────────────────────────────────────────────────────────────┤
│  Components                                                 │
│  ├─ Layout components (Header, Sidebar, Main)               │
│  ├─ Data tables and forms                                   │
│  ├─ Charts and visualizations                               │
│  └─ Modals and dialogs                                      │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### Browser Profile Management

**Profile Creation and Configuration**

- Fingerprint parameter configuration
- Proxy settings management
- Browser extension handling
- Target website optimization

**Profile Storage and Retrieval**

- Local file system storage
- Profile data serialization
- Data integrity validation

### Task Automation System

**Task Orchestration**

- Browser instance management
- Task scheduling and execution
- Progress monitoring and reporting
- Error handling and recovery

**Browser Automation**

- [playwright-with-fingerprints](https://github.com/CheshireCaat/playwright-with-fingerprints) integration
- Fingerprint application
- Human-like behavior simulation
- Screenshot capture and logging

### Data Import/Export

**Export Capabilities**

- Complete Browser Profile archives
- Individual component export (fingerprints, cookies, local storage, etc.)
- Batch export operations

**Import Capabilities**

- Archive validation and import
- Data integrity verification
- Conflict resolution
- Progress tracking

## Integration Points

### FingerprintSwitcher Integration(Custom Database)

```
┌─────────────────────────────────────────────────────────────┐
│                 FingerprintSwitcher API                     │
├─────────────────────────────────────────────────────────────┤
│  Fingerprint Database Access                                │
│  ├─ Private fingerprint database                            │
│  ├─ CustomServers private databases                         │
│  ├─ Fingerprint parameter modification                      │
│  └─ Real-time fingerprint application                       │
└─────────────────────────────────────────────────────────────┘
```

### Playwright with Fingerprints Integration

```
┌─────────────────────────────────────────────────────────────┐
│              Playwright with Fingerprints                   │
├─────────────────────────────────────────────────────────────┤
│  Browser Instance Management                                │
│  ├─ Chromium browser launching                              │
│  ├─ Fingerprint application                                 │
│  ├─ Stealth automation features                             │
│  └─ Anti-detection capabilities                             │
└─────────────────────────────────────────────────────────────┘
```

### System Integration

**File System**

- Browser Profile data storage
- Configuration file management
- Log file handling
- Temporary file cleanup

**Network**

- Proxy configuration and testing
- Internet connectivity validation
- API communication
- Download and upload operations

## Data Management(planned)

### Browser Profile Data Structure

```typescript
interface BrowserProfile {
    id: string;
    name: string;
    fingerprint: FingerprintConfig;
    proxy: ProxyConfig;
    extensions: ExtensionConfig[];
    browsing_history: BrowsingHistoryData;
    cookies: CookieData[];
    local_storage: LocalStorageData;
    created_at: Date;
    last_used: Date;
    status: ProfileStatus;
    tags: string[];
}
```

### Task Data Structure

```typescript
interface AutomationTask {
    id: string;
    name: string;
    type: TaskType;
    browser_profile_id: string;
    target_website: string;
    parameters: TaskParameters;
    status: TaskStatus;
    progress: number;
    started_at: Date;
    completed_at?: Date;
    logs: TaskLog[];
    screenshots: Screenshot[];
}
```

### Storage Strategy

**Local Storage**

- SQLite/MariaDb database for structured data
- File system for binary data (screenshots, archives)
- JSON/YAML configuration files for settings

---

This architecture overview provides the foundation for understanding the technical implementation of `Ghost Automator
Pro`. For specific implementation details, see the [Implementation Guide](IMPLEMENTATION_GUIDE.md)
and [Development Guide](../../DEVELOPMENT.md).
