graph TD
  %% Define link styles
  linkStyle default stroke:#666,stroke-width:2px

  %% User interaction flow - Blue
  A[User] -->|Interacts with| B[Vue Components]
  B -->|Calls| C[Vue Methods]
  C -->|Accesses| D[window.electronAPI]

  %% Frontend to backend flow - Purple
  D -->|Invokes| E[IPC Bridge]
  E -->|Sends via| F[IPC Channel]
  F -->|Receives| G[Main Process]
  G -->|Routes to| H[IPC Handlers]
  H -->|Calls| I[Services]
  I -->|Executes| J[Business Logic]

  %% Backend to frontend flow - Green
  J -->|Returns Data| I
  I -->|Returns| H
  H -->|Sends Response| G
  G -->|Replies via| F
  F -->|Receives| E
  E -->|Returns to| D

  %% UI update flow - Orange
  D -->|Updates| C
  C -->|Sets| K[Vue Data]
  K -->|Triggers| L[Reactive Template]
  L -->|Renders| M[Updated UI]
  M --> A

  %% Color the links by flow type
  linkStyle 0,1,2 stroke:#1E88E5,stroke-width:2px
  linkStyle 3,4,5,6,7,8 stroke:#7B1FA2,stroke-width:2px
  linkStyle 9,10,11,12,13,14 stroke:#2E7D32,stroke-width:2px
  linkStyle 15,16,17,18,19 stroke:#FF8F00,stroke-width:2px

  subgraph "Vue.js Frontend"
    B["Vue Components<br/>(ElectronIntegration,<br/>PlaywrightIntegration,<br/>FingerprintPlaywrightIntegration)"]
    C["Vue Methods<br/>(async methods calling electronAPI)"]
    K["Vue Data<br/>(reactive state)"]
    L["Reactive Template<br/>(v-if, v-for, v-bind)"]
    M["Updated UI<br/>(rendered DOM)"]
  end

  subgraph "Electron Renderer Process"
    D["window.electronAPI<br/>(exposed by contextBridge)"]
    E["ipcRenderer<br/>(invoke, send, on)"]
  end

  subgraph "Electron Preload Script"
    N["contextBridge.exposeInMainWorld()<br/>(secure API exposure)"]
    E -.- N
  end

  subgraph "Electron Main Process"
    G["app, BrowserWindow, ipcMain<br/>(handle, on)"]
    H["Handlers<br/>(PlaywrightHandlers,<br/>FingerprintPlaywrightHandlers)"]
    I["Services<br/>(PlaywrightService,<br/>FingerprintPlaywrightService)"]
    J["Business Logic<br/>(Playwright browser automation)"]
  end

  subgraph "IPC Communication"
    F["IPC Channel<br/>(named channels for specific operations)"]
  end

  style A fill: #e1f5fe, stroke: #039be5
  style B fill: #f3e5f5, stroke: #9c27b0
  style C fill: #f3e5f5, stroke: #9c27b0
  style D fill: #d1c4e9, stroke: #673ab7
  style E fill: #d1c4e9, stroke: #673ab7
  style F fill: #ffecb3, stroke: #ffa000
  style G fill: #c8e6c9, stroke: #4caf50
  style H fill: #c8e6c9, stroke: #4caf50
  style I fill: #a5d6a7, stroke: #2e7d32
  style J fill: #a5d6a7, stroke: #2e7d32
  style K fill: #f3e5f5, stroke: #9c27b0
  style L fill: #f3e5f5, stroke: #9c27b0
  style M fill: #f3e5f5, stroke: #9c27b0
  style N fill: #d1c4e9, stroke: #673ab7
