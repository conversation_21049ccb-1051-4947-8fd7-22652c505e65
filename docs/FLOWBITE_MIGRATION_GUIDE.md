# Flowbite Migration Guide: From Vuetify to Flowbite Vue

This guide provides step-by-step instructions for migrating from Vuetify to Flowbite Vue components in the Ghost Automator Pro application.

## Table of Contents

- [Overview](#overview)
- [Migration Strategy](#migration-strategy)
- [Component Mapping](#component-mapping)
- [Layout Migration](#layout-migration)
- [Form Components Migration](#form-components-migration)
- [Data Display Migration](#data-display-migration)
- [Navigation Migration](#navigation-migration)
- [Theme and Styling Migration](#theme-and-styling-migration)
- [Best Practices](#best-practices)

## Overview

The migration from Vuetify to Flowbite Vue involves:

- **Component Replacement**: Replacing Vuetify components with Flowbite Vue equivalents
- **Styling Update**: Moving from Vuetify's Material Design to Flowbite's Tailwind-based design
- **Theme System**: Implementing Flowbite's dark/light mode system
- **Layout Restructure**: Adapting layouts to use Flowbite's component structure

## Migration Strategy

### Phase 1: Setup and Configuration
1. Install Flowbite Vue and Tailwind CSS
2. Configure Tailwind CSS with Flowbite plugin
3. Update build configuration
4. Create theme management system

### Phase 2: Layout Migration
1. Replace Vuetify layout components
2. Update navigation structure
3. Implement responsive design with Tailwind

### Phase 3: Component Migration
1. Replace form components
2. Update data display components
3. Migrate navigation components
4. Update feedback components

### Phase 4: Styling and Theme
1. Implement dark/light mode
2. Update color schemes
3. Ensure responsive design
4. Test accessibility

## Component Mapping

### Core Layout Components

| Vuetify | Flowbite Vue | Notes |
|---------|--------------|-------|
| `v-app` | `div` with Tailwind classes | Use `antialiased bg-gray-50 dark:bg-gray-900` |
| `v-app-bar` | `FwbNavbar` | Top navigation bar |
| `v-navigation-drawer` | `FwbSidebar` | Side navigation |
| `v-main` | `div` with `p-4 sm:ml-64` | Main content area |
| `v-container` | `div` with `container mx-auto` | Content container |
| `v-row` | `div` with `grid` or `flex` | Grid/flex layout |
| `v-col` | `div` with `col-span-*` | Grid columns |

### Form Components

| Vuetify | Flowbite Vue | Notes |
|---------|--------------|-------|
| `v-text-field` | `FwbInput` | Text input field |
| `v-textarea` | `FwbTextarea` | Multi-line text input |
| `v-select` | `FwbSelect` | Dropdown selection |
| `v-checkbox` | `FwbCheckbox` | Checkbox input |
| `v-radio` | `FwbRadio` | Radio button |
| `v-switch` | `FwbToggle` | Toggle switch |
| `v-btn` | `FwbButton` | Button component |
| `v-form` | `form` with Tailwind classes | Form wrapper |

### Data Display Components

| Vuetify | Flowbite Vue | Notes |
|---------|--------------|-------|
| `v-card` | `FwbCard` | Card component |
| `v-data-table` | `FwbTable` | Data table |
| `v-list` | `FwbListGroup` | List component |
| `v-chip` | `FwbBadge` | Badge/chip component |
| `v-avatar` | `FwbAvatar` | Avatar component |
| `v-progress-linear` | `FwbProgress` | Progress bar |
| `v-alert` | `FwbAlert` | Alert message |

### Navigation Components

| Vuetify | Flowbite Vue | Notes |
|---------|--------------|-------|
| `v-tabs` | `FwbTabs` | Tab navigation |
| `v-menu` | `FwbDropdown` | Dropdown menu |
| `v-breadcrumbs` | `FwbBreadcrumb` | Breadcrumb navigation |
| `v-pagination` | `FwbPagination` | Pagination component |

## Layout Migration

### Before (Vuetify)

```vue
<template>
  <v-app>
    <v-app-bar app>
      <v-app-bar-title>Ghost Automator Pro</v-app-bar-title>
    </v-app-bar>
    
    <v-navigation-drawer app>
      <v-list>
        <v-list-item to="/">Dashboard</v-list-item>
        <v-list-item to="/profiles">Profiles</v-list-item>
      </v-list>
    </v-navigation-drawer>
    
    <v-main>
      <v-container>
        <router-view />
      </v-container>
    </v-main>
  </v-app>
</template>
```

### After (Flowbite Vue)

```vue
<template>
  <div class="antialiased bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <FwbNavbar class="fixed top-0 z-50 w-full">
      <template #logo>
        <FwbNavbarLogo alt="Ghost Automator Pro" link="/">
          Ghost Automator Pro
        </FwbNavbarLogo>
      </template>
    </FwbNavbar>

    <!-- Sidebar -->
    <FwbSidebar class="fixed top-0 left-0 z-40 w-64 h-screen pt-20">
      <FwbSidebarGroup>
        <FwbSidebarItem href="/" :is-active="$route.path === '/'">
          <template #icon>
            <DashboardIcon />
          </template>
          Dashboard
        </FwbSidebarItem>
        <FwbSidebarItem href="/profiles" :is-active="$route.path === '/profiles'">
          <template #icon>
            <ProfileIcon />
          </template>
          Browser Profiles
        </FwbSidebarItem>
      </FwbSidebarGroup>
    </FwbSidebar>

    <!-- Main Content -->
    <div class="p-4 sm:ml-64">
      <div class="p-4 mt-14">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  FwbNavbar,
  FwbNavbarLogo,
  FwbSidebar,
  FwbSidebarGroup,
  FwbSidebarItem
} from 'flowbite-vue'
</script>
```

## Form Components Migration

### Before (Vuetify)

```vue
<template>
  <v-form @submit.prevent="submitForm">
    <v-text-field
      v-model="form.name"
      label="Profile Name"
      required
    />
    
    <v-select
      v-model="form.browserType"
      :items="browserOptions"
      label="Browser Type"
    />
    
    <v-checkbox
      v-model="form.useProxy"
      label="Enable Proxy"
    />
    
    <v-btn type="submit" color="primary">
      Create Profile
    </v-btn>
  </v-form>
</template>
```

### After (Flowbite Vue)

```vue
<template>
  <form @submit.prevent="submitForm" class="space-y-6">
    <div>
      <FwbLabel for="profileName" value="Profile Name" />
      <FwbInput
        id="profileName"
        v-model="form.name"
        type="text"
        placeholder="Enter profile name"
        required
      />
    </div>

    <div>
      <FwbLabel for="browserType" value="Browser Type" />
      <FwbSelect
        id="browserType"
        v-model="form.browserType"
        :options="browserOptions"
        placeholder="Select browser type"
      />
    </div>

    <FwbCheckbox
      v-model="form.useProxy"
      label="Enable Proxy"
    />

    <FwbButton color="blue" type="submit">
      Create Profile
    </FwbButton>
  </form>
</template>

<script setup lang="ts">
import {
  FwbLabel,
  FwbInput,
  FwbSelect,
  FwbCheckbox,
  FwbButton
} from 'flowbite-vue'
</script>
```

## Data Display Migration

### Before (Vuetify)

```vue
<template>
  <v-card>
    <v-card-title>Browser Profiles</v-card-title>
    <v-data-table
      :headers="headers"
      :items="profiles"
      :items-per-page="10"
    >
      <template v-slot:item.status="{ item }">
        <v-chip :color="getStatusColor(item.status)">
          {{ item.status }}
        </v-chip>
      </template>
      <template v-slot:item.actions="{ item }">
        <v-btn icon @click="editProfile(item)">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
      </template>
    </v-data-table>
  </v-card>
</template>
```

### After (Flowbite Vue)

```vue
<template>
  <FwbCard>
    <template #header>
      <h3 class="text-xl font-bold text-gray-900 dark:text-white">
        Browser Profiles
      </h3>
    </template>
    
    <FwbTable>
      <FwbTableHead>
        <FwbTableHeadCell>Name</FwbTableHeadCell>
        <FwbTableHeadCell>Browser Type</FwbTableHeadCell>
        <FwbTableHeadCell>Status</FwbTableHeadCell>
        <FwbTableHeadCell>Actions</FwbTableHeadCell>
      </FwbTableHead>
      <FwbTableBody>
        <FwbTableRow v-for="profile in profiles" :key="profile.id">
          <FwbTableCell>{{ profile.name }}</FwbTableCell>
          <FwbTableCell>{{ profile.browserType }}</FwbTableCell>
          <FwbTableCell>
            <FwbBadge :color="getStatusColor(profile.status)">
              {{ profile.status }}
            </FwbBadge>
          </FwbTableCell>
          <FwbTableCell>
            <FwbButton size="xs" color="blue" @click="editProfile(profile)">
              Edit
            </FwbButton>
          </FwbTableCell>
        </FwbTableRow>
      </FwbTableBody>
    </FwbTable>
  </FwbCard>
</template>

<script setup lang="ts">
import {
  FwbCard,
  FwbTable,
  FwbTableHead,
  FwbTableHeadCell,
  FwbTableBody,
  FwbTableRow,
  FwbTableCell,
  FwbBadge,
  FwbButton
} from 'flowbite-vue'
</script>
```

## Theme and Styling Migration

### 1. Dark Mode Implementation

```typescript
// src/composables/useTheme.ts
import { ref, watch } from 'vue'

export function useTheme() {
  const isDark = ref(false)

  const toggleTheme = () => {
    isDark.value = !isDark.value
    updateTheme()
  }

  const updateTheme = () => {
    if (isDark.value) {
      document.documentElement.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      document.documentElement.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }
  }

  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
    updateTheme()
  }

  return {
    isDark,
    toggleTheme,
    initTheme
  }
}
```

### 2. Theme Toggle Component

```vue
<template>
  <FwbButton
    color="alternative"
    size="sm"
    @click="toggleTheme"
    class="p-2"
  >
    <SunIcon v-if="isDark" class="w-5 h-5" />
    <MoonIcon v-else class="w-5 h-5" />
  </FwbButton>
</template>

<script setup lang="ts">
import { FwbButton } from 'flowbite-vue'
import { useTheme } from '@/composables/useTheme'

const { isDark, toggleTheme } = useTheme()
</script>
```

## Best Practices

### 1. Component Organization
- Keep Flowbite Vue components in separate files
- Use TypeScript for better type safety
- Implement proper prop validation

### 2. Styling Guidelines
- Use Tailwind utility classes for custom styling
- Maintain consistent spacing with Tailwind spacing scale
- Follow Flowbite's design system guidelines

### 3. Accessibility
- Ensure proper ARIA attributes are maintained
- Test keyboard navigation
- Verify color contrast ratios

### 4. Performance
- Use component lazy loading where appropriate
- Optimize bundle size by importing only needed components
- Implement proper caching strategies

### 5. Testing
- Update component tests for new Flowbite components
- Test dark/light mode switching
- Verify responsive behavior across devices

This migration guide provides a comprehensive approach to transitioning from Vuetify to Flowbite Vue while maintaining functionality and improving the overall user experience.
